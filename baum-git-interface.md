# WP Git Interface - Complete Installation Package

## 📦 Quick Installation Guide

### Step 1: Create Plugin Directory Structure

Create the following directory structure in your WordPress installation:

```
/wp-content/plugins/wp-git-interface/
├── wp-git-interface.php
├── includes/
│   └── class-git-operations.php
├── assets/
│   ├── script.js
│   └── style.css
└── README.md
```

### Step 2: Create Main Plugin File

Save the main PHP code from the first artifact as:
`/wp-content/plugins/wp-git-interface/wp-git-interface.php`

### Step 3: Add Git Operations Class

1. Create the includes directory:
```bash
mkdir /wp-content/plugins/wp-git-interface/includes
```

2. Save the enhanced PHP functions as:
`/wp-content/plugins/wp-git-interface/includes/class-git-operations.php`

3. Add this line to the main plugin file after the class definition:
```php
require_once WPGI_PLUGIN_DIR . 'includes/class-git-operations.php';
```

### Step 4: Add JavaScript

Save the JavaScript code as:
`/wp-content/plugins/wp-git-interface/assets/script.js`

### Step 5: Add CSS

Save the CSS code as:
`/wp-content/plugins/wp-git-interface/assets/style.css`

### Step 6: Update Main Plugin File

Add this to the `enqueue_assets()` function in the main plugin file:

```php
// Add after Tailwind CSS enqueue
wp_enqueue_style('wpgi-style', WPGI_PLUGIN_URL . 'assets/style.css', array(), WPGI_VERSION);
```

### Step 7: Create Repository Directory

```bash
# Create the repository storage directory
sudo mkdir /wp-content/git-repos
sudo chown www-data:www-data /wp-content/git-repos
sudo chmod 755 /wp-content/git-repos
```

### Step 8: Activate Plugin

1. Go to WordPress Admin → Plugins
2. Find "WP Git Interface"
3. Click "Activate"

### Step 9: Add Repositories

SSH into your server and run:

```bash
cd /wp-content/git-repos

# Clone a public repository
git clone --bare https://github.com/wordpress/wordpress.git

# Clone a private repository (requires SSH key)
git clone --bare **************:username/private-repo.git

# Mirror a repository (keeps in sync)
git clone --mirror https://github.com/username/repo.git
```

### Step 10: Display on Your Site

1. Create a new page in WordPress
2. Add the shortcode: `[git_interface]`
3. Publish the page
4. Visit the page to see your Git interface

## 🚀 Advanced Setup

### Enable Git Smart HTTP (Optional)

For Apache, add to `.htaccess`:

```apache
# Git Smart HTTP Support
SetEnv GIT_PROJECT_ROOT /var/www/html/wp-content/git-repos
SetEnv GIT_HTTP_EXPORT_ALL
ScriptAlias /git/ /usr/lib/git-core/git-http-backend/

<Directory "/usr/lib/git-core">
    Options +ExecCGI
    Require all granted
</Directory>

<Directory "/var/www/html/wp-content/git-repos">
    Options +ExecCGI
    Require all granted
</Directory>
```

For Nginx:

```nginx
location ~ ^/git(/.*) {
    client_max_body_size 0;
    fastcgi_pass unix:/var/run/fcgiwrap.socket;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME /usr/lib/git-core/git-http-backend;
    fastcgi_param GIT_HTTP_EXPORT_ALL "";
    fastcgi_param GIT_PROJECT_ROOT /var/www/html/wp-content/git-repos;
    fastcgi_param PATH_INFO $1;
}
```

### Set Up Automatic Updates

Create a cron job to update repositories:

```bash
# Edit crontab
crontab -e

# Add this line to update all repos every hour
0 * * * * cd /wp-content/git-repos && for repo in *.git; do cd "$repo" && git fetch --all --prune && cd ..; done
```

### Configure Webhooks (GitHub)

1. In your GitHub repository, go to Settings → Webhooks
2. Add webhook URL: `https://yoursite.com/wp-json/wpgi/v1/webhook`
3. Set content type to `application/json`
4. Select events to trigger webhook

### Performance Optimization

Add to `wp-config.php`:

```php
// Enable caching
define('WPGI_ENABLE_CACHE', true);
define('WPGI_CACHE_TIME', 3600); // 1 hour

// Set custom repository directory
define('WPGI_REPOS_DIR', '/custom/path/to/repos');

// Enable debug mode (development only)
define('WPGI_DEBUG', false);

// Limit number of commits shown
define('WPGI_COMMITS_PER_PAGE', 50);
```

## 🔒 Security Configuration

### Restrict Repository Access

Add to your theme's `functions.php`:

```php
// Only allow logged-in users to view repositories
add_filter('wpgi_can_view_repo', function($can_view, $repo) {
    return is_user_logged_in();
}, 10, 2);

// Restrict specific repositories to certain roles
add_filter('wpgi_can_view_repo', function($can_view, $repo) {
    if ($repo === 'private-repo.git') {
        return current_user_can('administrator');
    }
    return $can_view;
}, 10, 2);

// Disable certain features
add_filter('wpgi_enable_downloads', '__return_false'); // Disable downloads
add_filter('wpgi_enable_raw_view', '__return_false');  // Disable raw view
```

### Password Protect Repositories

For basic authentication on Git operations:

```bash
# Create htpasswd file
htpasswd -c /wp-content/git-repos/.htpasswd username

# Add to Apache config
<Directory "/wp-content/git-repos">
    AuthType Basic
    AuthName "Git Repository Access"
    AuthUserFile /wp-content/git-repos/.htpasswd
    Require valid-user
</Directory>
```

## 🎨 Customization Examples

### Custom Repository Card Design

Add to your theme's CSS:

```css
/* Custom repository card colors */
.wpgi-repo-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.wpgi-repo-card h3 {
    color: white !important;
}

/* Custom activity graph colors */
.wpgi-activity-day.level-1 { background: #9be9a8; }
.wpgi-activity-day.level-2 { background: #40c463; }
.wpgi-activity-day.level-3 { background: #30a14e; }
.wpgi-activity-day.level-4 { background: #216e39; }
```

### Add Custom Repository Badges

```php
// Add to functions.php
add_action('wpgi_repo_card_badges', function($repo_name) {
    if ($repo_name === 'featured-repo.git') {
        echo '<span class="badge badge-featured">⭐ Featured</span>';
    }
});
```

### Custom File Icons

```php
// Add custom file type icons
add_filter('wpgi_file_icon', function($icon, $filename) {
    $extension = pathinfo($filename, PATHINFO_EXTENSION);
    
    $custom_icons = [
        'vue' => 'fab fa-vuejs',
        'react' => 'fab fa-react',
        'docker' => 'fab fa-docker',
        'yml' => 'fas fa-cog',
        'yaml' => 'fas fa-cog'
    ];
    
    if (isset($custom_icons[$extension])) {
        return $custom_icons[$extension];
    }
    
    return $icon;
}, 10, 2);
```

## 📊 Monitoring & Analytics

### Track Repository Views

```php
// Add to functions.php
add_action('wpgi_repository_viewed', function($repo_name) {
    // Log view to database or analytics
    $views = get_option('wpgi_repo_views', []);
    $views[$repo_name] = ($views[$repo_name] ?? 0) + 1;
    update_option('wpgi_repo_views', $views);
});
```

### Display Popular Repositories

```php
// Show most viewed repositories
function wpgi_get_popular_repos($limit = 5) {
    $views = get_option('wpgi_repo_views', []);
    arsort($views);
    return array_slice($views, 0, $limit, true);
}
```

## 🐛 Troubleshooting Guide

### Issue: Repositories Not Showing

**Check Git Installation:**
```bash
which git
git --version
```

**Check Directory Permissions:**
```bash
ls -la /wp-content/git-repos
# Should show: drwxr-xr-x with www-data ownership
```

**Check PHP exec() Function:**
```php
// Add to test file
if (function_exists('exec')) {
    echo "exec() is enabled";
} else {
    echo "exec() is disabled";
}
```

### Issue: Syntax Highlighting Not Working

1. Check browser console for errors
2. Verify Prism.js is loading:
```javascript
console.log(typeof Prism !== 'undefined' ? 'Prism loaded' : 'Prism not loaded');
```
3. Clear browser cache
4. Check CDN accessibility

### Issue: Performance Problems

**Enable Query Monitor plugin to debug:**
```bash
wp plugin install query-monitor --activate
```

**Check Git repository size:**
```bash
du -sh /wp-content/git-repos/*
```

**Optimize large repositories:**
```bash
cd /wp-content/git-repos/large-repo.git
git gc --aggressive --prune=now
```

## 🔧 Server Requirements Checklist

- [ ] PHP 7.2+ with exec() enabled
- [ ] Git 2.0+ installed
- [ ] WordPress 5.0+
- [ ] Apache/Nginx with .htaccess support
- [ ] Write permissions for wp-content directory
- [ ] Minimum 512MB PHP memory limit
- [ ] Modern browser with JavaScript enabled

## 📝 Testing Checklist

After installation, verify:

- [ ] Plugin activates without errors
- [ ] Repository directory exists and is writable
- [ ] Shortcode displays interface
- [ ] Repositories are listed correctly
- [ ] File browsing works
- [ ] Commit history displays
- [ ] Syntax highlighting works
- [ ] Search functionality works
- [ ] Mobile responsive design works
- [ ] Keyboard shortcuts work

## 🚑 Emergency Fixes

### Reset Plugin:
```sql
-- Remove all plugin data from database
DELETE FROM wp_options WHERE option_name LIKE 'wpgi_%';
```

### Reinstall Plugin:
```bash
# Backup repositories
cp -r /wp-content/git-repos /backup/git-repos

# Remove plugin
rm -rf /wp-content/plugins/wp-git-interface

# Reinstall
# ... follow installation steps ...

# Restore repositories
cp -r /backup/git-repos /wp-content/git-repos
```

### Debug Mode:
```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('WPGI_DEBUG', true);

// Check logs at:
// /wp-content/debug.log
```

## 📞 Support Resources

- **Documentation**: Check README.md in plugin directory
- **WordPress Forums**: https://wordpress.org/support/
- **Git Documentation**: https://git-scm.com/doc
- **Stack Overflow**: Tag with `wordpress` and `git`

## 🎉 Success Indicators

Your installation is successful when you can:
1. See the Git Interface menu in WordPress admin
2. View repositories on the frontend
3. Browse files within repositories
4. View commit history
5. Switch between branches
6. Search repository content

---

**Congratulations! Your WP Git Interface plugin is now fully installed and configured.**

For updates and additional features, check the plugin repository regularly.