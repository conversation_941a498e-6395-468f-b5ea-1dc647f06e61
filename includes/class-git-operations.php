<?php
class WPGitOperations {
    
    private $repos_dir;
    
    public function __construct($repos_dir) {
        $this->repos_dir = $repos_dir;
    }
    
    private function exec_git($repo, $command, $args = array()) {
        $repo_path = $this->repos_dir . '/' . $repo;
        if (!file_exists($repo_path)) return false;
        
        $cmd = 'cd ' . escapeshellarg($repo_path) . ' && git ' . $command;
        if (!empty($args)) {
            foreach ($args as $arg) {
                $cmd .= ' ' . escapeshellarg($arg);
            }
        }
        
        $output = array();
        $return_var = 0;
        exec($cmd . ' 2>&1', $output, $return_var);
        
        return array('success' => $return_var === 0, 'output' => $output, 'return_code' => $return_var);
    }
    
    public function get_file_tree($repo, $branch = 'HEAD', $path = '') {
        $tree_path = $branch;
        if (!empty($path)) $tree_path .= ':' . $path;
        
        $result = $this->exec_git($repo, 'ls-tree', array('--full-name', $tree_path));
        if (!$result['success']) return array();
        
        $files = array();
        foreach ($result['output'] as $line) {
            if (empty($line)) continue;
            
            if (preg_match('/^(\d+)\s+(blob|tree)\s+([0-9a-f]+)\s+(.+)$/', $line, $matches)) {
                $file_path = $matches[4];
                $file_name = basename($file_path);
                $is_dir = $matches[2] === 'tree';
                
                $file_info = $this->get_file_info($repo, $file_path, $branch);
                
                $files[] = array(
                    'name' => $file_name,
                    'path' => $file_path,
                    'type' => $is_dir ? 'directory' : 'file',
                    'size' => $is_dir ? null : $file_info['size'],
                    'last_commit' => $file_info['last_commit']
                );
            }
        }
        
        usort($files, function($a, $b) {
            if ($a['type'] === $b['type']) return strcasecmp($a['name'], $b['name']);
            return $a['type'] === 'directory' ? -1 : 1;
        });
        
        return $files;
    }
    
    public function get_file_content($repo, $path, $branch = 'HEAD') {
        $result = $this->exec_git($repo, 'show', array($branch . ':' . $path));
        if ($result['success']) return implode("\n", $result['output']);
        return false;
    }
    
    public function get_file_info($repo, $path, $branch = 'HEAD') {
        $info = array('size' => 0, 'last_commit' => array('hash' => '', 'message' => '', 'date' => ''));
        
        $size_result = $this->exec_git($repo, 'cat-file', array('-s', $branch . ':' . $path));
        if ($size_result['success'] && !empty($size_result['output'])) {
            $info['size'] = $this->format_file_size(intval($size_result['output'][0]));
        }
        
        $log_result = $this->exec_git($repo, 'log', array('-1', '--pretty=format:%H|%s|%at', $branch, '--', $path));
        if ($log_result['success'] && !empty($log_result['output'])) {
            $parts = explode('|', $log_result['output'][0], 3);
            if (count($parts) >= 3) {
                $info['last_commit'] = array(
                    'hash' => substr($parts[0], 0, 7),
                    'message' => strlen($parts[1]) > 50 ? substr($parts[1], 0, 47) . '...' : $parts[1],
                    'date' => $this->time_elapsed_string($parts[2])
                );
            }
        }
        
        return $info;
    }
    
    public function get_commits($repo, $branch = 'HEAD', $limit = 50, $skip = 0) {
        $result = $this->exec_git($repo, 'log', array(
            '--pretty=format:%H|%an|%ae|%at|%s|%b',
            '--max-count=' . $limit,
            '--skip=' . $skip,
            $branch
        ));
        
        if (!$result['success']) return array();
        
        $commits = array();
        foreach ($result['output'] as $line) {
            if (empty($line)) continue;
            
            $parts = explode('|', $line, 6);
            if (count($parts) >= 5) {
                $commits[] = array(
                    'hash' => $parts[0],
                    'short_hash' => substr($parts[0], 0, 7),
                    'author' => $parts[1],
                    'email' => $parts[2],
                    'timestamp' => $parts[3],
                    'date' => date('Y-m-d H:i:s', $parts[3]),
                    'relative_date' => $this->time_elapsed_string($parts[3]),
                    'subject' => $parts[4],
                    'body' => isset($parts[5]) ? $parts[5] : ''
                );
            }
        }
        
        return $commits;
    }
    
    public function get_branches($repo) {
        $result = $this->exec_git($repo, 'for-each-ref', array(
            '--format=%(refname:short)|%(committerdate:unix)|%(subject)',
            'refs/heads/'
        ));
        
        if (!$result['success']) return array();
        
        $branches = array();
        $current_branch = $this->get_current_branch($repo);
        
        foreach ($result['output'] as $line) {
            if (empty($line)) continue;
            
            $parts = explode('|', $line, 3);
            if (count($parts) >= 2) {
                $branch_name = $parts[0];
                $branches[] = array(
                    'name' => $branch_name,
                    'is_current' => ($branch_name === $current_branch),
                    'last_commit_date' => isset($parts[1]) ? date('Y-m-d H:i:s', $parts[1]) : '',
                    'relative_date' => isset($parts[1]) ? $this->time_elapsed_string($parts[1]) : '',
                    'last_commit_message' => isset($parts[2]) ? $parts[2] : ''
                );
            }
        }
        
        return $branches;
    }
    
    public function get_current_branch($repo) {
        $result = $this->exec_git($repo, 'symbolic-ref', array('--short', 'HEAD'));
        
        if ($result['success'] && !empty($result['output'])) {
            return trim($result['output'][0]);
        }
        
        $head_file = $this->repos_dir . '/' . $repo . '/HEAD';
        if (file_exists($head_file)) {
            $head = file_get_contents($head_file);
            if (preg_match('/ref: refs\/heads\/(.+)/', $head, $matches)) {
                return trim($matches[1]);
            }
        }
        
        return 'main';
    }
    
    public function get_commit_diff($repo, $commit_hash) {
        $info_result = $this->exec_git($repo, 'show', array(
            '--pretty=format:%H|%an|%ae|%at|%s|%b',
            '--no-patch',
            $commit_hash
        ));
        
        $commit_info = array();
        if ($info_result['success'] && !empty($info_result['output'])) {
            $parts = explode('|', $info_result['output'][0], 6);
            if (count($parts) >= 5) {
                $commit_info = array(
                    'hash' => $parts[0],
                    'short_hash' => substr($parts[0], 0, 7),
                    'author' => $parts[1],
                    'email' => $parts[2],
                    'date' => date('Y-m-d H:i:s', $parts[3]),
                    'relative_date' => $this->time_elapsed_string($parts[3]),
                    'subject' => $parts[4],
                    'body' => isset($parts[5]) ? $parts[5] : ''
                );
            }
        }
        
        $stats_result = $this->exec_git($repo, 'show', array('--stat', '--no-patch', $commit_hash));
        $stats = '';
        if ($stats_result['success']) {
            $stats = implode("\n", array_slice($stats_result['output'], 7));
        }
        
        $diff_result = $this->exec_git($repo, 'show', array('--patch', '--no-prefix', $commit_hash));
        $diff = '';
        if ($diff_result['success']) {
            $diff_lines = $diff_result['output'];
            $start_index = 0;
            foreach ($diff_lines as $i => $line) {
                if (strpos($line, 'diff --git') === 0) {
                    $start_index = $i;
                    break;
                }
            }
            $diff = implode("\n", array_slice($diff_lines, $start_index));
        }
        
        return array('commit' => $commit_info, 'stats' => $stats, 'diff' => $diff);
    }
    
    public function get_repo_stats($repo) {
        $stats = array(
            'commits' => 0,
            'branches' => 0,
            'tags' => 0,
            'contributors' => 0,
            'files' => 0,
            'size' => '0 KB'
        );
        
        $result = $this->exec_git($repo, 'rev-list', array('--count', '--all'));
        if ($result['success'] && !empty($result['output'])) {
            $stats['commits'] = intval($result['output'][0]);
        }
        
        $branches = $this->get_branches($repo);
        $stats['branches'] = count($branches);
        
        $result = $this->exec_git($repo, 'tag', array());
        if ($result['success']) {
            $stats['tags'] = count(array_filter($result['output']));
        }
        
        $result = $this->exec_git($repo, 'shortlog', array('-sn', 'HEAD'));
        if ($result['success']) {
            $stats['contributors'] = count($result['output']);
        }
        
        $result = $this->exec_git($repo, 'ls-files', array());
        if ($result['success']) {
            $stats['files'] = count($result['output']);
        }
        
        $repo_path = $this->repos_dir . '/' . $repo;
        $stats['size'] = $this->get_directory_size($repo_path);
        
        return $stats;
    }
    
    public function get_all_repositories() {
        $repos = array();
        
        if (is_dir($this->repos_dir)) {
            $dirs = scandir($this->repos_dir);
            foreach ($dirs as $dir) {
                if ($dir != '.' && $dir != '..' && $dir != 'users' && $dir != 'gists' && $dir != '.htaccess') {
                    if ($this->is_git_repo($this->repos_dir . '/' . $dir)) {
                        $repos[] = $dir;
                    }
                }
            }
        }
        
        $users_dir = $this->repos_dir . '/users';
        if (is_dir($users_dir)) {
            $users = scandir($users_dir);
            foreach ($users as $user) {
                if ($user != '.' && $user != '..') {
                    $user_repos_dir = $users_dir . '/' . $user;
                    if (is_dir($user_repos_dir)) {
                        $user_repos = scandir($user_repos_dir);
                        foreach ($user_repos as $repo) {
                            if ($repo != '.' && $repo != '..') {
                                if ($this->is_git_repo($user_repos_dir . '/' . $repo)) {
                                    $repos[] = 'users/' . $user . '/' . $repo;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return $repos;
    }
    
    private function is_git_repo($path) {
        return file_exists($path . '/HEAD') || 
               file_exists($path . '/.git/HEAD') ||
               file_exists($path . '/config');
    }
    
    public function get_file_language($filename) {
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        $languages = array(
            'php' => 'php',
            'js' => 'javascript',
            'py' => 'python',
            'rb' => 'ruby',
            'java' => 'java',
            'c' => 'c',
            'cpp' => 'cpp',
            'cs' => 'csharp',
            'go' => 'go',
            'rs' => 'rust',
            'html' => 'html',
            'css' => 'css',
            'json' => 'json',
            'yaml' => 'yaml',
            'yml' => 'yaml',
            'sql' => 'sql',
            'md' => 'markdown',
            'sh' => 'bash'
        );
        
        return $languages[$ext] ?? 'plaintext';
    }
    
    private function format_file_size($bytes) {
        if ($bytes == 0) return '0 B';
        $k = 1024;
        $sizes = array('B', 'KB', 'MB', 'GB');
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
    
    private function get_directory_size($path) {
        $bytes = 0;
        
        if (is_dir($path)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($path, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $bytes += $file->getSize();
                }
            }
        }
        
        return $this->format_file_size($bytes);
    }
    
    private function time_elapsed_string($timestamp) {
        if (!is_numeric($timestamp)) return 'Unknown';
        
        $time = time() - $timestamp;
        
        if ($time < 1) return 'just now';
        
        $tokens = array(
            31536000 => 'year',
            2592000 => 'month',
            604800 => 'week',
            86400 => 'day',
            3600 => 'hour',
            60 => 'minute',
            1 => 'second'
        );
        
        foreach ($tokens as $unit => $text) {
            if ($time < $unit) continue;
            $numberOfUnits = floor($time / $unit);
            return $numberOfUnits . ' ' . $text . (($numberOfUnits > 1) ? 's' : '') . ' ago';
        }
    }
}
