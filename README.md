# WP Git Interface Plugin

A comprehensive Git web interface for WordPress.

## Installation

1. Upload to `/wp-content/plugins/`
2. Activate through WordPress admin
3. Create `/wp-content/git-repos/` directory
4. Clone repositories: `git clone --bare URL`
5. Add `[git_interface]` shortcode to any page

## Features

- Repository browsing
- File viewing with syntax highlighting
- Commit history
- Branch management
- Markdown rendering
- User repositories
- Search functionality

## Requirements

- WordPress 5.0+
- PHP 7.2+
- Git 2.0+
- exec() function enabled

## Usage

### Shortcodes

- `[git_interface]` - Display all repositories
- `[git_repo repo="name.git"]` - Display specific repository
- `[git_interface user="username"]` - Display user repositories

### Adding Repositories

```bash
cd /wp-content/git-repos/
git clone --bare https://github.com/user/repo.git
```

## License

GPL v2 or later