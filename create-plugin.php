<?php
/**
 * WP Git Interface Plugin ZIP Creator
 * 
 * Run this script: php create-wp-git-plugin-zip.php
 * This will create wp-git-interface.zip with all plugin files
 */

echo "Creating WP Git Interface Plugin ZIP...\n";

// Create temporary directory
$temp_dir = sys_get_temp_dir() . '/wp-git-interface-' . time();
$plugin_dir = $temp_dir . '/wp-git-interface';
mkdir($plugin_dir, 0755, true);
mkdir($plugin_dir . '/includes', 0755, true);
mkdir($plugin_dir . '/assets', 0755, true);

// Main plugin file
$main_plugin_file = '<?php
/**
 * Plugin Name: WP Git Interface
 * Plugin URI: https://example.com/wp-git-interface
 * Description: A Git web interface for WordPress, similar to cgit or GitLab
 * Version: 2.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

if (!defined(\'ABSPATH\')) exit;

define(\'WPGI_PLUGIN_DIR\', plugin_dir_path(__FILE__));
define(\'WPGI_PLUGIN_URL\', plugin_dir_url(__FILE__));
define(\'WPGI_VERSION\', \'2.0.0\');

require_once WPGI_PLUGIN_DIR . \'includes/class-git-operations.php\';

class WPGitInterface {
    
    private static $instance = null;
    private $repos_dir;
    private $git_ops;
    
    public static function getInstance() {
        if (self::$instance == null) {
            self::$instance = new WPGitInterface();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->repos_dir = defined(\'WPGI_REPOS_DIR\') ? WPGI_REPOS_DIR : WP_CONTENT_DIR . \'/git-repos\';
        $this->git_ops = new WPGitOperations($this->repos_dir);
        $this->init();
    }
    
    private function init() {
        if (!file_exists($this->repos_dir)) {
            wp_mkdir_p($this->repos_dir);
            wp_mkdir_p($this->repos_dir . \'/users\');
            wp_mkdir_p($this->repos_dir . \'/gists\');
            file_put_contents($this->repos_dir . \'/.htaccess\', \'Deny from all\');
        }
        
        add_action(\'wp_enqueue_scripts\', array($this, \'enqueue_assets\'));
        add_action(\'admin_menu\', array($this, \'add_admin_menu\'));
        
        add_shortcode(\'git_interface\', array($this, \'render_interface\'));
        add_shortcode(\'git_repo\', array($this, \'render_single_repo\'));
        
        $this->register_ajax_handlers();
    }
    
    private function register_ajax_handlers() {
        $actions = array(
            \'wpgi_get_file_content\',
            \'wpgi_view_file\',
            \'wpgi_view_raw\',
            \'wpgi_download_file\',
            \'wpgi_view_diff\',
            \'wpgi_get_commits\',
            \'wpgi_get_repositories\'
        );
        
        foreach ($actions as $action) {
            add_action(\'wp_ajax_\' . $action, array($this, \'ajax_\' . str_replace(\'wpgi_\', \'\', $action)));
            add_action(\'wp_ajax_nopriv_\' . $action, array($this, \'ajax_\' . str_replace(\'wpgi_\', \'\', $action)));
        }
    }
    
    public function enqueue_assets() {
        global $post;
        if (!is_a($post, \'WP_Post\') || !has_shortcode($post->post_content, \'git_\')) return;
        
        wp_enqueue_style(\'wpgi-fontawesome\', \'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\', array(), \'6.5.1\');
        wp_enqueue_style(\'wpgi-prism\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css\', array(), \'1.29.0\');
        wp_enqueue_style(\'wpgi-prism-line-numbers\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css\', array(), \'1.29.0\');
        wp_enqueue_style(\'wpgi-style\', WPGI_PLUGIN_URL . \'assets/style.css\', array(), WPGI_VERSION);
        
        wp_enqueue_script(\'wpgi-mustache\', \'https://cdnjs.cloudflare.com/ajax/libs/mustache.js/4.2.0/mustache.min.js\', array(), \'4.2.0\', true);
        wp_enqueue_script(\'wpgi-marked\', \'https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js\', array(), \'4.3.0\', true);
        wp_enqueue_script(\'wpgi-prism\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js\', array(), \'1.29.0\', true);
        wp_enqueue_script(\'wpgi-prism-autoloader\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js\', array(\'wpgi-prism\'), \'1.29.0\', true);
        wp_enqueue_script(\'wpgi-prism-line-numbers\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js\', array(\'wpgi-prism\'), \'1.29.0\', true);
        wp_enqueue_script(\'wpgi-script\', WPGI_PLUGIN_URL . \'assets/script.js\', array(\'jquery\', \'wpgi-mustache\', \'wpgi-marked\'), WPGI_VERSION, true);
        
        wp_localize_script(\'wpgi-script\', \'wpgi_ajax\', array(
            \'ajax_url\' => admin_url(\'admin-ajax.php\'),
            \'nonce\' => wp_create_nonce(\'wpgi_nonce\'),
            \'plugin_url\' => WPGI_PLUGIN_URL,
            \'site_url\' => site_url(),
            \'is_logged_in\' => is_user_logged_in()
        ));
    }
    
    public function add_admin_menu() {
        add_menu_page(\'Git Interface\', \'Git Interface\', \'manage_options\', \'wp-git-interface\', array($this, \'admin_page\'), \'dashicons-media-code\', 30);
    }
    
    public function admin_page() {
        $repos = $this->git_ops->get_all_repositories();
        ?>
        <div class="wrap">
            <h1>Git Interface Settings</h1>
            <div class="card">
                <h2>Repository Directory: <code><?php echo esc_html($this->repos_dir); ?></code></h2>
                <h3>Repositories (<?php echo count($repos); ?>):</h3>
                <ul>
                <?php foreach ($repos as $repo): ?>
                    <li><?php echo esc_html($repo); ?></li>
                <?php endforeach; ?>
                </ul>
                <h3>Usage:</h3>
                <p>Add <code>[git_interface]</code> to any page to display repositories.</p>
                <p>Use <code>[git_repo repo="name.git"]</code> to display a specific repository.</p>
            </div>
        </div>
        <?php
    }
    
    public function render_interface($atts) {
        $atts = shortcode_atts(array(\'user\' => \'\', \'view\' => \'grid\'), $atts);
        ob_start();
        ?>
        <div class="wpgi-wrapper wpgi-container">
            <div class="wpgi-header">
                <h2 class="wpgi-title"><i class="fab fa-git-alt"></i> <span>Git Repositories</span></h2>
                <div class="wpgi-controls">
                    <input type="text" class="wpgi-search" placeholder="Search repositories..." />
                </div>
            </div>
            <div class="wpgi-content" id="wpgi-main-content">
                <div class="wpgi-loading"><i class="fas fa-spinner fa-spin"></i> <span>Loading...</span></div>
            </div>
        </div>
        
        <script type="text/template" id="wpgi-repo-list-template">
            <div class="wpgi-repo-grid">
                {{#repos}}
                <div class="wpgi-card wpgi-repo-card" data-repo="{{name}}">
                    <div class="wpgi-repo-header">
                        <i class="fas fa-book"></i>
                        <h3>{{display_name}}</h3>
                    </div>
                    <p class="wpgi-repo-description">{{description}}</p>
                    <button onclick="wpgiLoadRepo(\'{{name}}\')" class="wpgi-view-repo">View</button>
                </div>
                {{/repos}}
            </div>
        </script>
        
        <script>jQuery(document).ready(function() { wpgiLoadRepositories(<?php echo json_encode($atts); ?>); });</script>
        <?php
        return ob_get_clean();
    }
    
    public function render_single_repo($atts) {
        $atts = shortcode_atts(array(\'repo\' => \'\', \'branch\' => \'main\'), $atts);
        if (empty($atts[\'repo\'])) return \'<div class="wpgi-error">No repository specified</div>\';
        
        ob_start();
        ?>
        <div class="wpgi-wrapper wpgi-repo-view" data-repo="<?php echo esc_attr($atts[\'repo\']); ?>">
            <div class="wpgi-repo-container" id="wpgi-repo-<?php echo esc_attr(sanitize_title($atts[\'repo\'])); ?>">
                <div class="wpgi-loading"><i class="fas fa-spinner fa-spin"></i> <span>Loading repository...</span></div>
            </div>
        </div>
        
        <script type="text/template" id="wpgi-repo-view-template">
            <div class="wpgi-repo-header">
                <h2>{{repo_name}}</h2>
            </div>
            <div class="wpgi-repo-nav">
                <button class="wpgi-tab active" data-tab="code"><i class="fas fa-code"></i> Code</button>
                <button class="wpgi-tab" data-tab="commits"><i class="fas fa-history"></i> Commits</button>
            </div>
            <div class="wpgi-repo-content">
                <div class="wpgi-tab-content" id="code-tab">
                    <div class="wpgi-file-browser">
                        <div class="wpgi-breadcrumb">{{#breadcrumb}}<a href="#" onclick="wpgiNavigate(\'{{path}}\')">{{name}}</a>{{#separator}}<span>/</span>{{/separator}}{{/breadcrumb}}</div>
                        <table class="wpgi-file-list">
                            <thead><tr><th>Name</th><th>Last commit</th><th>Last update</th></tr></thead>
                            <tbody>
                                {{#files}}
                                <tr class="wpgi-file-row" onclick="wpgi{{type}}Click(\'{{path}}\')">
                                    <td><i class="{{icon}}"></i> <span>{{name}}</span></td>
                                    <td>{{last_commit}}</td>
                                    <td>{{last_update}}</td>
                                </tr>
                                {{/files}}
                            </tbody>
                        </table>
                    </div>
                    {{#readme}}
                    <div class="wpgi-readme">
                        <div class="wpgi-readme-header"><i class="fas fa-book-open"></i> README.md</div>
                        <div class="wpgi-readme-content wpgi-markdown">{{{readme_html}}}</div>
                    </div>
                    {{/readme}}
                </div>
                <div class="wpgi-tab-content" id="commits-tab" style="display:none;"></div>
            </div>
        </script>
        
        <script type="text/template" id="wpgi-file-viewer-template">
            <div class="wpgi-file-viewer">
                <div class="wpgi-file-header">
                    <div class="wpgi-file-info"><i class="{{icon}}"></i> <span>{{filename}}</span></div>
                    <div class="wpgi-file-actions">
                        <button onclick="wpgiViewRaw(\'{{path}}\')"><i class="fas fa-file-alt"></i> Raw</button>
                        <button onclick="wpgiDownload(\'{{path}}\')"><i class="fas fa-download"></i> Download</button>
                        <button onclick="wpgiCloseViewer()"><i class="fas fa-times"></i></button>
                    </div>
                </div>
                <div class="wpgi-file-content">
                    <pre class="line-numbers"><code class="language-{{language}}">{{{content}}}</code></pre>
                </div>
            </div>
        </script>
        
        <script>jQuery(document).ready(function() { wpgiLoadSingleRepo(<?php echo json_encode($atts); ?>); });</script>
        <?php
        return ob_get_clean();
    }
    
    // AJAX Handlers
    public function ajax_get_repositories() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        $repos = $this->git_ops->get_all_repositories();
        $formatted = array();
        
        foreach ($repos as $repo) {
            $stats = $this->git_ops->get_repo_stats($repo);
            $formatted[] = array(
                \'name\' => $repo,
                \'display_name\' => str_replace(\'.git\', \'\', basename($repo)),
                \'description\' => \'Repository\',
                \'commits\' => $stats[\'commits\'],
                \'branches\' => $stats[\'branches\']
            );
        }
        
        wp_send_json_success(array(\'repos\' => $formatted));
    }
    
    public function ajax_get_file_content() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        
        $repo = sanitize_text_field($_POST[\'repo\'] ?? \'\');
        $path = sanitize_text_field($_POST[\'path\'] ?? \'\');
        $branch = sanitize_text_field($_POST[\'branch\'] ?? \'main\');
        
        if (empty($repo)) wp_send_json_error(\'Repository not specified\');
        
        $files = $this->git_ops->get_file_tree($repo, $branch, $path);
        
        $readme = null;
        if (empty($path)) {
            $readme_content = $this->git_ops->get_file_content($repo, \'README.md\', $branch);
            if ($readme_content) {
                $readme = array(
                    \'content\' => $readme_content,
                    \'html\' => $this->parse_markdown($readme_content)
                );
            }
        }
        
        $breadcrumb = $this->build_breadcrumb($repo, $path);
        $branches = $this->git_ops->get_branches($repo);
        
        wp_send_json_success(array(
            \'files\' => $this->format_files($files),
            \'readme\' => $readme,
            \'breadcrumb\' => $breadcrumb,
            \'branches\' => $branches,
            \'repo_name\' => str_replace(\'.git\', \'\', $repo)
        ));
    }
    
    public function ajax_view_file() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        
        $repo = sanitize_text_field($_POST[\'repo\'] ?? \'\');
        $path = sanitize_text_field($_POST[\'path\'] ?? \'\');
        $branch = sanitize_text_field($_POST[\'branch\'] ?? \'main\');
        
        if (empty($repo) || empty($path)) wp_send_json_error(\'Invalid parameters\');
        
        $content = $this->git_ops->get_file_content($repo, $path, $branch);
        if ($content === false) wp_send_json_error(\'File not found\');
        
        $filename = basename($path);
        $language = $this->git_ops->get_file_language($filename);
        
        wp_send_json_success(array(
            \'filename\' => $filename,
            \'path\' => $path,
            \'content\' => htmlspecialchars($content),
            \'language\' => $language,
            \'icon\' => \'fas fa-file-code\'
        ));
    }
    
    public function ajax_view_raw() {
        $repo = sanitize_text_field($_GET[\'repo\'] ?? \'\');
        $path = sanitize_text_field($_GET[\'path\'] ?? \'\');
        $branch = sanitize_text_field($_GET[\'branch\'] ?? \'main\');
        
        if (empty($repo) || empty($path)) wp_die(\'Invalid parameters\');
        
        $content = $this->git_ops->get_file_content($repo, $path, $branch);
        if ($content === false) wp_die(\'File not found\');
        
        header(\'Content-Type: text/plain\');
        echo $content;
        exit;
    }
    
    public function ajax_download_file() {
        $repo = sanitize_text_field($_GET[\'repo\'] ?? \'\');
        $path = sanitize_text_field($_GET[\'path\'] ?? \'\');
        $branch = sanitize_text_field($_GET[\'branch\'] ?? \'main\');
        
        if (empty($repo) || empty($path)) wp_die(\'Invalid parameters\');
        
        $content = $this->git_ops->get_file_content($repo, $path, $branch);
        if ($content === false) wp_die(\'File not found\');
        
        header(\'Content-Type: application/octet-stream\');
        header(\'Content-Disposition: attachment; filename="\' . basename($path) . \'"\');
        echo $content;
        exit;
    }
    
    public function ajax_view_diff() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        
        $repo = sanitize_text_field($_POST[\'repo\'] ?? \'\');
        $commit = sanitize_text_field($_POST[\'commit\'] ?? \'\');
        
        if (empty($repo) || empty($commit)) wp_send_json_error(\'Invalid parameters\');
        
        $diff_data = $this->git_ops->get_commit_diff($repo, $commit);
        if (!$diff_data) wp_send_json_error(\'Commit not found\');
        
        $formatted_diff = $this->format_diff($diff_data[\'diff\']);
        
        wp_send_json_success(array(
            \'commit\' => $diff_data[\'commit\'],
            \'stats\' => $diff_data[\'stats\'],
            \'diff_html\' => $formatted_diff
        ));
    }
    
    public function ajax_get_commits() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        
        $repo = sanitize_text_field($_POST[\'repo\'] ?? \'\');
        $branch = sanitize_text_field($_POST[\'branch\'] ?? \'main\');
        
        if (empty($repo)) wp_send_json_error(\'Repository not specified\');
        
        $commits = $this->git_ops->get_commits($repo, $branch, 50);
        wp_send_json_success($commits);
    }
    
    private function format_files($files) {
        $formatted = array();
        foreach ($files as $file) {
            $icon = $file[\'type\'] === \'directory\' ? \'fas fa-folder\' : \'fas fa-file\';
            $formatted[] = array(
                \'name\' => $file[\'name\'],
                \'path\' => $file[\'path\'],
                \'type\' => $file[\'type\'] === \'directory\' ? \'Folder\' : \'File\',
                \'icon\' => $icon,
                \'last_commit\' => $file[\'last_commit\'][\'message\'] ?? \'\',
                \'last_update\' => $file[\'last_commit\'][\'date\'] ?? \'\'
            );
        }
        return $formatted;
    }
    
    private function format_diff($diff) {
        if (empty($diff)) return \'<div class="wpgi-no-diff">No changes</div>\';
        
        $lines = explode("\\n", $diff);
        $html = \'<div class="wpgi-diff-viewer"><pre class="language-diff line-numbers"><code>\';
        
        foreach ($lines as $line) {
            if (strpos($line, \'+++\') === 0) {
                $html .= \'<span class="wpgi-diff-file-new">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } elseif (strpos($line, \'---\') === 0) {
                $html .= \'<span class="wpgi-diff-file-old">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } elseif (strpos($line, \'@@\') === 0) {
                $html .= \'<span class="wpgi-diff-hunk">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } elseif (strpos($line, \'+\') === 0) {
                $html .= \'<span class="wpgi-diff-added">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } elseif (strpos($line, \'-\') === 0) {
                $html .= \'<span class="wpgi-diff-removed">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } else {
                $html .= htmlspecialchars($line) . "\\n";
            }
        }
        
        $html .= \'</code></pre></div>\';
        return $html;
    }
    
    private function build_breadcrumb($repo, $path) {
        $breadcrumb = array(array(\'name\' => str_replace(\'.git\', \'\', $repo), \'path\' => \'\', \'separator\' => !empty($path)));
        
        if (!empty($path)) {
            $parts = explode(\'/\', $path);
            $current_path = \'\';
            foreach ($parts as $i => $part) {
                $current_path .= ($i > 0 ? \'/\' : \'\') . $part;
                $breadcrumb[] = array(\'name\' => $part, \'path\' => $current_path, \'separator\' => $i < count($parts) - 1);
            }
        }
        
        return $breadcrumb;
    }
    
    private function parse_markdown($content) {
        // Basic markdown parsing
        $html = htmlspecialchars($content);
        $html = preg_replace(\'/^### (.*?)$/m\', \'<h3>$1</h3>\', $html);
        $html = preg_replace(\'/^## (.*?)$/m\', \'<h2>$1</h2>\', $html);
        $html = preg_replace(\'/^# (.*?)$/m\', \'<h1>$1</h1>\', $html);
        $html = preg_replace(\'/\\*\\*\\*(.*?)\\*\\*\\*/s\', \'<strong><em>$1</em></strong>\', $html);
        $html = preg_replace(\'/\\*\\*(.*?)\\*\\*/s\', \'<strong>$1</strong>\', $html);
        $html = preg_replace(\'/\\*(.*?)\\*/s\', \'<em>$1</em>\', $html);
        $html = preg_replace(\'/\\[([^\\]]+)\\]\\(([^\\)]+)\\)/\', \'<a href="$2">$1</a>\', $html);
        $html = preg_replace(\'/```([a-z]*)\\n(.*?)```/s\', \'<pre><code class="language-$1">$2</code></pre>\', $html);
        $html = preg_replace(\'/`([^`]+)`/\', \'<code>$1</code>\', $html);
        $html = nl2br($html);
        return $html;
    }
}

add_action(\'plugins_loaded\', function() { WPGitInterface::getInstance(); });

register_activation_hook(__FILE__, function() {
    $repos_dir = defined(\'WPGI_REPOS_DIR\') ? WPGI_REPOS_DIR : WP_CONTENT_DIR . \'/git-repos\';
    if (!file_exists($repos_dir)) {
        wp_mkdir_p($repos_dir);
        file_put_contents($repos_dir . \'/.htaccess\', \'Deny from all\');
    }
    flush_rewrite_rules();
});

register_deactivation_hook(__FILE__, function() { flush_rewrite_rules(); });
';

file_put_contents($plugin_dir . '/wp-git-interface.php', $main_plugin_file);

// Git Operations Class
$git_operations_file = '<?php
class WPGitOperations {
    
    private $repos_dir;
    
    public function __construct($repos_dir) {
        $this->repos_dir = $repos_dir;
    }
    
    private function exec_git($repo, $command, $args = array()) {
        $repo_path = $this->repos_dir . \'/\' . $repo;
        if (!file_exists($repo_path)) return false;
        
        $cmd = \'cd \' . escapeshellarg($repo_path) . \' && git \' . $command;
        if (!empty($args)) {
            foreach ($args as $arg) {
                $cmd .= \' \' . escapeshellarg($arg);
            }
        }
        
        $output = array();
        $return_var = 0;
        exec($cmd . \' 2>&1\', $output, $return_var);
        
        return array(\'success\' => $return_var === 0, \'output\' => $output, \'return_code\' => $return_var);
    }
    
    public function get_file_tree($repo, $branch = \'HEAD\', $path = \'\') {
        $tree_path = $branch;
        if (!empty($path)) $tree_path .= \':\' . $path;
        
        $result = $this->exec_git($repo, \'ls-tree\', array(\'--full-name\', $tree_path));
        if (!$result[\'success\']) return array();
        
        $files = array();
        foreach ($result[\'output\'] as $line) {
            if (empty($line)) continue;
            
            if (preg_match(\'/^(\\d+)\\s+(blob|tree)\\s+([0-9a-f]+)\\s+(.+)$/\', $line, $matches)) {
                $file_path = $matches[4];
                $file_name = basename($file_path);
                $is_dir = $matches[2] === \'tree\';
                
                $file_info = $this->get_file_info($repo, $file_path, $branch);
                
                $files[] = array(
                    \'name\' => $file_name,
                    \'path\' => $file_path,
                    \'type\' => $is_dir ? \'directory\' : \'file\',
                    \'size\' => $is_dir ? null : $file_info[\'size\'],
                    \'last_commit\' => $file_info[\'last_commit\']
                );
            }
        }
        
        usort($files, function($a, $b) {
            if ($a[\'type\'] === $b[\'type\']) return strcasecmp($a[\'name\'], $b[\'name\']);
            return $a[\'type\'] === \'directory\' ? -1 : 1;
        });
        
        return $files;
    }
    
    public function get_file_content($repo, $path, $branch = \'HEAD\') {
        $result = $this->exec_git($repo, \'show\', array($branch . \':\' . $path));
        if ($result[\'success\']) return implode("\\n", $result[\'output\']);
        return false;
    }
    
    public function get_file_info($repo, $path, $branch = \'HEAD\') {
        $info = array(\'size\' => 0, \'last_commit\' => array(\'hash\' => \'\', \'message\' => \'\', \'date\' => \'\'));
        
        $size_result = $this->exec_git($repo, \'cat-file\', array(\'-s\', $branch . \':\' . $path));
        if ($size_result[\'success\'] && !empty($size_result[\'output\'])) {
            $info[\'size\'] = $this->format_file_size(intval($size_result[\'output\'][0]));
        }
        
        $log_result = $this->exec_git($repo, \'log\', array(\'-1\', \'--pretty=format:%H|%s|%at\', $branch, \'--\', $path));
        if ($log_result[\'success\'] && !empty($log_result[\'output\'])) {
            $parts = explode(\'|\', $log_result[\'output\'][0], 3);
            if (count($parts) >= 3) {
                $info[\'last_commit\'] = array(
                    \'hash\' => substr($parts[0], 0, 7),
                    \'message\' => strlen($parts[1]) > 50 ? substr($parts[1], 0, 47) . \'...\' : $parts[1],
                    \'date\' => $this->time_elapsed_string($parts[2])
                );
            }
        }
        
        return $info;
    }
    
    public function get_commits($repo, $branch = \'HEAD\', $limit = 50, $skip = 0) {
        $result = $this->exec_git($repo, \'log\', array(
            \'--pretty=format:%H|%an|%ae|%at|%s|%b\',
            \'--max-count=\' . $limit,
            \'--skip=\' . $skip,
            $branch
        ));
        
        if (!$result[\'success\']) return array();
        
        $commits = array();
        foreach ($result[\'output\'] as $line) {
            if (empty($line)) continue;
            
            $parts = explode(\'|\', $line, 6);
            if (count($parts) >= 5) {
                $commits[] = array(
                    \'hash\' => $parts[0],
                    \'short_hash\' => substr($parts[0], 0, 7),
                    \'author\' => $parts[1],
                    \'email\' => $parts[2],
                    \'timestamp\' => $parts[3],
                    \'date\' => date(\'Y-m-d H:i:s\', $parts[3]),
                    \'relative_date\' => $this->time_elapsed_string($parts[3]),
                    \'subject\' => $parts[4],
                    \'body\' => isset($parts[5]) ? $parts[5] : \'\'
                );
            }
        }
        
        return $commits;
    }
    
    public function get_branches($repo) {
        $result = $this->exec_git($repo, \'for-each-ref\', array(
            \'--format=%(refname:short)|%(committerdate:unix)|%(subject)\',
            \'refs/heads/\'
        ));
        
        if (!$result[\'success\']) return array();
        
        $branches = array();
        $current_branch = $this->get_current_branch($repo);
        
        foreach ($result[\'output\'] as $line) {
            if (empty($line)) continue;
            
            $parts = explode(\'|\', $line, 3);
            if (count($parts) >= 2) {
                $branch_name = $parts[0];
                $branches[] = array(
                    \'name\' => $branch_name,
                    \'is_current\' => ($branch_name === $current_branch),
                    \'last_commit_date\' => isset($parts[1]) ? date(\'Y-m-d H:i:s\', $parts[1]) : \'\',
                    \'relative_date\' => isset($parts[1]) ? $this->time_elapsed_string($parts[1]) : \'\',
                    \'last_commit_message\' => isset($parts[2]) ? $parts[2] : \'\'
                );
            }
        }
        
        return $branches;
    }
    
    public function get_current_branch($repo) {
        $result = $this->exec_git($repo, \'symbolic-ref\', array(\'--short\', \'HEAD\'));
        
        if ($result[\'success\'] && !empty($result[\'output\'])) {
            return trim($result[\'output\'][0]);
        }
        
        $head_file = $this->repos_dir . \'/\' . $repo . \'/HEAD\';
        if (file_exists($head_file)) {
            $head = file_get_contents($head_file);
            if (preg_match(\'/ref: refs\\/heads\\/(.+)/\', $head, $matches)) {
                return trim($matches[1]);
            }
        }
        
        return \'main\';
    }
    
    public function get_commit_diff($repo, $commit_hash) {
        $info_result = $this->exec_git($repo, \'show\', array(
            \'--pretty=format:%H|%an|%ae|%at|%s|%b\',
            \'--no-patch\',
            $commit_hash
        ));
        
        $commit_info = array();
        if ($info_result[\'success\'] && !empty($info_result[\'output\'])) {
            $parts = explode(\'|\', $info_result[\'output\'][0], 6);
            if (count($parts) >= 5) {
                $commit_info = array(
                    \'hash\' => $parts[0],
                    \'short_hash\' => substr($parts[0], 0, 7),
                    \'author\' => $parts[1],
                    \'email\' => $parts[2],
                    \'date\' => date(\'Y-m-d H:i:s\', $parts[3]),
                    \'relative_date\' => $this->time_elapsed_string($parts[3]),
                    \'subject\' => $parts[4],
                    \'body\' => isset($parts[5]) ? $parts[5] : \'\'
                );
            }
        }
        
        $stats_result = $this->exec_git($repo, \'show\', array(\'--stat\', \'--no-patch\', $commit_hash));
        $stats = \'\';
        if ($stats_result[\'success\']) {
            $stats = implode("\\n", array_slice($stats_result[\'output\'], 7));
        }
        
        $diff_result = $this->exec_git($repo, \'show\', array(\'--patch\', \'--no-prefix\', $commit_hash));
        $diff = \'\';
        if ($diff_result[\'success\']) {
            $diff_lines = $diff_result[\'output\'];
            $start_index = 0;
            foreach ($diff_lines as $i => $line) {
                if (strpos($line, \'diff --git\') === 0) {
                    $start_index = $i;
                    break;
                }
            }
            $diff = implode("\\n", array_slice($diff_lines, $start_index));
        }
        
        return array(\'commit\' => $commit_info, \'stats\' => $stats, \'diff\' => $diff);
    }
    
    public function get_repo_stats($repo) {
        $stats = array(
            \'commits\' => 0,
            \'branches\' => 0,
            \'tags\' => 0,
            \'contributors\' => 0,
            \'files\' => 0,
            \'size\' => \'0 KB\'
        );
        
        $result = $this->exec_git($repo, \'rev-list\', array(\'--count\', \'--all\'));
        if ($result[\'success\'] && !empty($result[\'output\'])) {
            $stats[\'commits\'] = intval($result[\'output\'][0]);
        }
        
        $branches = $this->get_branches($repo);
        $stats[\'branches\'] = count($branches);
        
        $result = $this->exec_git($repo, \'tag\', array());
        if ($result[\'success\']) {
            $stats[\'tags\'] = count(array_filter($result[\'output\']));
        }
        
        $result = $this->exec_git($repo, \'shortlog\', array(\'-sn\', \'HEAD\'));
        if ($result[\'success\']) {
            $stats[\'contributors\'] = count($result[\'output\']);
        }
        
        $result = $this->exec_git($repo, \'ls-files\', array());
        if ($result[\'success\']) {
            $stats[\'files\'] = count($result[\'output\']);
        }
        
        $repo_path = $this->repos_dir . \'/\' . $repo;
        $stats[\'size\'] = $this->get_directory_size($repo_path);
        
        return $stats;
    }
    
    public function get_all_repositories() {
        $repos = array();
        
        if (is_dir($this->repos_dir)) {
            $dirs = scandir($this->repos_dir);
            foreach ($dirs as $dir) {
                if ($dir != \'.\' && $dir != \'..\' && $dir != \'users\' && $dir != \'gists\' && $dir != \'.htaccess\') {
                    if ($this->is_git_repo($this->repos_dir . \'/\' . $dir)) {
                        $repos[] = $dir;
                    }
                }
            }
        }
        
        $users_dir = $this->repos_dir . \'/users\';
        if (is_dir($users_dir)) {
            $users = scandir($users_dir);
            foreach ($users as $user) {
                if ($user != \'.\' && $user != \'..\') {
                    $user_repos_dir = $users_dir . \'/\' . $user;
                    if (is_dir($user_repos_dir)) {
                        $user_repos = scandir($user_repos_dir);
                        foreach ($user_repos as $repo) {
                            if ($repo != \'.\' && $repo != \'..\') {
                                if ($this->is_git_repo($user_repos_dir . \'/\' . $repo)) {
                                    $repos[] = \'users/\' . $user . \'/\' . $repo;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return $repos;
    }
    
    private function is_git_repo($path) {
        return file_exists($path . \'/HEAD\') || 
               file_exists($path . \'/.git/HEAD\') ||
               file_exists($path . \'/config\');
    }
    
    public function get_file_language($filename) {
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        $languages = array(
            \'php\' => \'php\',
            \'js\' => \'javascript\',
            \'py\' => \'python\',
            \'rb\' => \'ruby\',
            \'java\' => \'java\',
            \'c\' => \'c\',
            \'cpp\' => \'cpp\',
            \'cs\' => \'csharp\',
            \'go\' => \'go\',
            \'rs\' => \'rust\',
            \'html\' => \'html\',
            \'css\' => \'css\',
            \'json\' => \'json\',
            \'yaml\' => \'yaml\',
            \'yml\' => \'yaml\',
            \'sql\' => \'sql\',
            \'md\' => \'markdown\',
            \'sh\' => \'bash\'
        );
        
        return $languages[$ext] ?? \'plaintext\';
    }
    
    private function format_file_size($bytes) {
        if ($bytes == 0) return \'0 B\';
        $k = 1024;
        $sizes = array(\'B\', \'KB\', \'MB\', \'GB\');
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . \' \' . $sizes[$i];
    }
    
    private function get_directory_size($path) {
        $bytes = 0;
        
        if (is_dir($path)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($path, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $bytes += $file->getSize();
                }
            }
        }
        
        return $this->format_file_size($bytes);
    }
    
    private function time_elapsed_string($timestamp) {
        if (!is_numeric($timestamp)) return \'Unknown\';
        
        $time = time() - $timestamp;
        
        if ($time < 1) return \'just now\';
        
        $tokens = array(
            31536000 => \'year\',
            2592000 => \'month\',
            604800 => \'week\',
            86400 => \'day\',
            3600 => \'hour\',
            60 => \'minute\',
            1 => \'second\'
        );
        
        foreach ($tokens as $unit => $text) {
            if ($time < $unit) continue;
            $numberOfUnits = floor($time / $unit);
            return $numberOfUnits . \' \' . $text . (($numberOfUnits > 1) ? \'s\' : \'\') . \' ago\';
        }
    }
}
';

file_put_contents($plugin_dir . '/includes/class-git-operations.php', $git_operations_file);

// JavaScript file
$js_file = '(function($) {
    "use strict";

    window.WPGI = window.WPGI || {};
    
    WPGI.currentRepo = null;
    WPGI.currentPath = "";
    WPGI.currentBranch = "main";
    WPGI.openViewers = [];
    
    window.wpgiLoadRepositories = function(options) {
        showLoading("#wpgi-main-content");
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_get_repositories",
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                renderTemplate("wpgi-repo-list-template", response.data, "#wpgi-main-content");
            }
        });
    };
    
    window.wpgiLoadSingleRepo = function(options) {
        WPGI.currentRepo = options.repo;
        WPGI.currentBranch = options.branch || "main";
        
        const container = "#wpgi-repo-" + options.repo.replace(/[^a-zA-Z0-9]/g, "-");
        showLoading(container);
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_get_file_content",
            repo: options.repo,
            path: "",
            branch: WPGI.currentBranch,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                const data = response.data;
                data.repo_name = options.repo;
                renderTemplate("wpgi-repo-view-template", data, container);
                initializeTabs();
                if (data.readme) {
                    setTimeout(function() {
                        Prism.highlightAllUnder($(container)[0]);
                    }, 100);
                }
            }
        });
    };
    
    window.wpgiLoadRepo = function(repo) {
        closeAllViewers();
        const $card = $(\'.wpgi-repo-card[data-repo="\' + repo + \'"]\');
        
        if (!$card.next(".wpgi-repo-expanded").length) {
            $card.after(\'<div class="wpgi-repo-expanded" id="repo-expanded-\' + repo.replace(/[^a-zA-Z0-9]/g, "-") + \'"></div>\');
        }
        
        const container = "#repo-expanded-" + repo.replace(/[^a-zA-Z0-9]/g, "-");
        
        wpgiLoadSingleRepo({
            repo: repo,
            container: container
        });
        
        $("html, body").animate({
            scrollTop: $(container).offset().top - 100
        }, 500);
    };
    
    window.wpgiNavigate = function(path) {
        loadFileTree(WPGI.currentRepo, path, WPGI.currentBranch);
    };
    
    window.wpgiFolderClick = function(path) {
        loadFileTree(WPGI.currentRepo, path, WPGI.currentBranch);
    };
    
    window.wpgiFileClick = function(path) {
        closeAllViewers();
        showLoading(".wpgi-file-viewer-container", "Loading file...");
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_view_file",
            repo: WPGI.currentRepo,
            path: path,
            branch: WPGI.currentBranch,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                if (!$(".wpgi-file-viewer-container").length) {
                    $(".wpgi-file-browser").after(\'<div class="wpgi-file-viewer-container"></div>\');
                }
                
                renderTemplate("wpgi-file-viewer-template", response.data, ".wpgi-file-viewer-container");
                WPGI.openViewers.push(".wpgi-file-viewer-container");
                
                setTimeout(function() {
                    Prism.highlightAllUnder($(".wpgi-file-viewer-container")[0]);
                }, 100);
            }
        });
    };
    
    window.wpgiViewRaw = function(path) {
        const url = wpgi_ajax.ajax_url + "?action=wpgi_view_raw&repo=" + 
                   encodeURIComponent(WPGI.currentRepo) + "&path=" + encodeURIComponent(path) + 
                   "&branch=" + WPGI.currentBranch + "&nonce=" + wpgi_ajax.nonce;
        window.open(url, "_blank");
    };
    
    window.wpgiDownload = function(path) {
        const url = wpgi_ajax.ajax_url + "?action=wpgi_download_file&repo=" + 
                   encodeURIComponent(WPGI.currentRepo) + "&path=" + encodeURIComponent(path) + 
                   "&branch=" + WPGI.currentBranch + "&nonce=" + wpgi_ajax.nonce;
        window.location.href = url;
    };
    
    window.wpgiCloseViewer = function() {
        $(".wpgi-file-viewer-container").fadeOut(function() {
            $(this).remove();
        });
    };
    
    window.wpgiViewCommit = function(repo, hash) {
        showLoading(".wpgi-commit-viewer", "Loading commit...");
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_view_diff",
            repo: repo,
            commit: hash,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                const viewer = $(\'<div class="wpgi-commit-viewer wpgi-card">\' +
                    \'<div class="wpgi-commit-header">\' +
                    \'<button onclick="wpgiCloseCommit()" class="wpgi-btn-back">\' +
                    \'<i class="fas fa-arrow-left"></i> Back</button>\' +
                    \'<h3>Commit \' + response.data.commit.short_hash + \'</h3></div>\' +
                    \'<div class="wpgi-commit-info">\' +
                    \'<div><i class="fas fa-user"></i> \' + response.data.commit.author + \'</div>\' +
                    \'<div><i class="fas fa-calendar"></i> \' + response.data.commit.relative_date + \'</div>\' +
                    \'<div class="wpgi-commit-message">\' + response.data.commit.subject + \'</div></div>\' +
                    \'<div class="wpgi-commit-diff">\' + response.data.diff_html + \'</div></div>\');
                
                $(".wpgi-tab-content:visible").html(viewer);
                
                setTimeout(function() {
                    Prism.highlightAllUnder(viewer[0]);
                }, 100);
            }
        });
    };
    
    window.wpgiCloseCommit = function() {
        wpgiLoadCommits(WPGI.currentRepo);
    };
    
    window.wpgiLoadCommits = function(repo) {
        const container = "#commits-tab";
        showLoading(container);
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_get_commits",
            repo: repo || WPGI.currentRepo,
            branch: WPGI.currentBranch,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                renderCommits(response.data, container);
            }
        });
    };
    
    function loadFileTree(repo, path, branch) {
        WPGI.currentPath = path;
        showLoading(".wpgi-file-browser");
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_get_file_content",
            repo: repo,
            path: path,
            branch: branch,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                const html = renderFileList(response.data.files);
                $(".wpgi-file-list tbody").html(html);
                
                const breadcrumbHtml = renderBreadcrumb(response.data.breadcrumb);
                $(".wpgi-breadcrumb").html(breadcrumbHtml);
                
                if (response.data.readme) {
                    $(".wpgi-readme-content").html(response.data.readme.html);
                    $(".wpgi-readme").show();
                } else {
                    $(".wpgi-readme").hide();
                }
            }
        });
    }
    
    function renderFileList(files) {
        let html = "";
        files.forEach(function(file) {
            const clickHandler = file.type === "Folder" ? "wpgiFolderClick" : "wpgiFileClick";
            html += \'<tr class="wpgi-file-row" onclick="\' + clickHandler + \'(\\\'\' + file.path + \'\\\')">\';
            html += \'<td><i class="\' + file.icon + \'"></i> <span>\' + file.name + \'</span></td>\';
            html += \'<td>\' + (file.last_commit || "-") + \'</td>\';
            html += \'<td>\' + (file.last_update || "-") + \'</td>\';
            html += \'</tr>\';
        });
        return html || \'<tr><td colspan="3" class="wpgi-empty">No files found</td></tr>\';
    }
    
    function renderBreadcrumb(breadcrumb) {
        let html = "";
        breadcrumb.forEach(function(item, index) {
            if (index > 0) html += \'<span class="wpgi-separator">/</span>\';
            html += \'<a href="#" onclick="wpgiNavigate(\\\'\' + item.path + \'\\\')">\' + item.name + \'</a>\';
        });
        return html;
    }
    
    function renderCommits(commits, container) {
        let html = \'<div class="wpgi-commits-list">\';
        commits.forEach(function(commit) {
            html += \'<div class="wpgi-commit-item" onclick="wpgiViewCommit(\\\'\' + 
                    WPGI.currentRepo + \'\\\', \\\'\' + commit.hash + \'\\\')">\';
            html += \'<div class="wpgi-commit-header">\';
            html += \'<span class="wpgi-commit-hash">\' + commit.short_hash + \'</span>\';
            html += \'<span class="wpgi-commit-date">\' + commit.relative_date + \'</span></div>\';
            html += \'<div class="wpgi-commit-message">\' + commit.subject + \'</div>\';
            html += \'<div class="wpgi-commit-author"><i class="fas fa-user"></i> \' + commit.author + \'</div>\';
            html += \'</div>\';
        });
        html += \'</div>\';
        $(container).html(html);
    }
    
    function renderTemplate(templateId, data, container) {
        const template = $("#" + templateId).html();
        if (template && window.Mustache) {
            const rendered = Mustache.render(template, data);
            $(container).html(rendered);
        }
    }
    
    function showLoading(container, message) {
        const msg = message || "Loading...";
        $(container).html(\'<div class="wpgi-loading"><i class="fas fa-spinner fa-spin"></i> <span>\' + msg + \'</span></div>\');
    }
    
    function closeAllViewers() {
        WPGI.openViewers.forEach(function(viewer) {
            $(viewer).remove();
        });
        WPGI.openViewers = [];
        $(".wpgi-repo-expanded").remove();
    }
    
    function initializeTabs() {
        $(".wpgi-tab").on("click", function() {
            const tab = $(this).data("tab");
            
            $(".wpgi-tab").removeClass("active");
            $(this).addClass("active");
            
            $(".wpgi-tab-content").hide();
            $("#" + tab + "-tab").show();
            
            if (tab === "commits" && $("#commits-tab").is(":empty")) {
                wpgiLoadCommits(WPGI.currentRepo);
            }
        });
    }
    
    $(document).ready(function() {
        $(".wpgi-search").on("keyup", function() {
            const term = $(this).val().toLowerCase();
            $(".wpgi-repo-card").each(function() {
                const name = $(this).find("h3").text().toLowerCase();
                const desc = $(this).find(".wpgi-repo-description").text().toLowerCase();
                
                if (name.includes(term) || desc.includes(term)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });
        
        $(document).on("keydown", function(e) {
            if (e.key === "Escape") {
                closeAllViewers();
            }
        });
    });
    
})(jQuery);';

file_put_contents($plugin_dir . '/assets/script.js', $js_file);

// CSS file
$css_file = '.wpgi-wrapper {
    --wpgi-primary: var(--color-primary, #000);
    --wpgi-accent: var(--color-accent, #0066cc);
    --wpgi-success: var(--color-green, #28a745);
    --wpgi-danger: var(--color-red, #dc3545);
    --wpgi-border-radius: var(--border-radius, 8px);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.wpgi-wrapper.wpgi-container {
    background: transparent !important;
    min-height: 400px;
}

.wpgi-wrapper .wpgi-card {
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.wpgi-wrapper .wpgi-repo-card {
    background: white !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: var(--wpgi-border-radius) !important;
    padding: 1.25rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.wpgi-wrapper .wpgi-repo-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    border-color: var(--wpgi-accent) !important;
}

.wpgi-wrapper .wpgi-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 2rem !important;
    padding: 1.5rem !important;
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
}

.wpgi-wrapper .wpgi-title {
    font-size: 1.75rem !important;
    font-weight: 700 !important;
    color: var(--wpgi-primary) !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.wpgi-wrapper .wpgi-repo-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)) !important;
    gap: 1.5rem !important;
}

.wpgi-wrapper .wpgi-file-browser {
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
    overflow: hidden !important;
}

.wpgi-wrapper .wpgi-file-list {
    width: 100% !important;
    border-collapse: collapse !important;
}

.wpgi-wrapper .wpgi-file-list th {
    padding: 0.75rem 1rem !important;
    text-align: left !important;
    font-weight: 600 !important;
    background: #f7f7f7 !important;
}

.wpgi-wrapper .wpgi-file-list td {
    padding: 0.75rem 1rem !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.wpgi-wrapper .wpgi-file-row:hover {
    background-color: #fafafa !important;
}

.wpgi-wrapper .wpgi-file-viewer {
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
    margin-top: 1.5rem !important;
    overflow: hidden !important;
}

.wpgi-wrapper pre[class*="language-"].line-numbers {
    position: relative !important;
    padding-left: 3.8em !important;
    counter-reset: linenumber !important;
}

.wpgi-wrapper .wpgi-diff-added {
    background-color: var(--wpgi-success) !important;
    color: white !important;
    display: block !important;
    padding: 0.125rem 0.5rem !important;
}

.wpgi-wrapper .wpgi-diff-removed {
    background-color: var(--wpgi-danger) !important;
    color: white !important;
    display: block !important;
    padding: 0.125rem 0.5rem !important;
}

.wpgi-wrapper .wpgi-loading {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 3rem !important;
    color: var(--wpgi-accent) !important;
    gap: 0.75rem !important;
}

.wpgi-wrapper .wpgi-loading i {
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.wpgi-wrapper .wpgi-readme {
    margin-top: 2rem !important;
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
}

.wpgi-wrapper .wpgi-readme-header {
    background: #f7f7f7 !important;
    padding: 1rem 1.5rem !important;
    font-weight: 600 !important;
}

.wpgi-wrapper .wpgi-markdown h1 {
    font-size: 2rem !important;
    margin: 1.5rem 0 1rem !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 1px solid #e0e0e0 !important;
}

.wpgi-wrapper .wpgi-markdown h2 {
    font-size: 1.5rem !important;
    margin: 1.25rem 0 0.75rem !important;
}

.wpgi-wrapper .wpgi-markdown code {
    background: #f7f7f7 !important;
    padding: 0.125rem 0.375rem !important;
    border-radius: 3px !important;
}

.wpgi-wrapper .wpgi-markdown pre {
    background: #2d2d30 !important;
    padding: 1rem !important;
    border-radius: var(--wpgi-border-radius) !important;
    overflow-x: auto !important;
}

.wpgi-wrapper .wpgi-markdown a {
    color: var(--wpgi-accent) !important;
    text-decoration: none !important;
}

.wpgi-wrapper .wpgi-tab {
    padding: 1rem 1.5rem !important;
    background: none !important;
    border: none !important;
    border-bottom: 2px solid transparent !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
}

.wpgi-wrapper .wpgi-tab.active {
    color: var(--wpgi-accent) !important;
    border-bottom-color: var(--wpgi-accent) !important;
}

.wpgi-wrapper .wpgi-commit-item {
    padding: 1rem !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: var(--wpgi-border-radius) !important;
    margin-bottom: 0.75rem !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
}

.wpgi-wrapper .wpgi-commit-item:hover {
    border-color: var(--wpgi-accent) !important;
    background: #fafafa !important;
}

@media (max-width: 768px) {
    .wpgi-wrapper .wpgi-repo-grid {
        grid-template-columns: 1fr !important;
    }
}';

file_put_contents($plugin_dir . '/assets/style.css', $css_file);

// Create README
$readme = '# WP Git Interface Plugin

A comprehensive Git web interface for WordPress.

## Installation

1. Upload to `/wp-content/plugins/`
2. Activate through WordPress admin
3. Create `/wp-content/git-repos/` directory
4. Clone repositories: `git clone --bare URL`
5. Add `[git_interface]` shortcode to any page

## Features

- Repository browsing
- File viewing with syntax highlighting
- Commit history
- Branch management
- Markdown rendering
- User repositories
- Search functionality

## Requirements

- WordPress 5.0+
- PHP 7.2+
- Git 2.0+
- exec() function enabled

## Usage

### Shortcodes

- `[git_interface]` - Display all repositories
- `[git_repo repo="name.git"]` - Display specific repository
- `[git_interface user="username"]` - Display user repositories

### Adding Repositories

```bash
cd /wp-content/git-repos/
git clone --bare https://github.com/user/repo.git
```

## License

GPL v2 or later';

file_put_contents($plugin_dir . '/README.md', $readme);

// Create the ZIP file
$zip = new ZipArchive();
$zip_file = 'wp-git-interface.zip';

if ($zip->open($zip_file, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
    // Add all files
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($plugin_dir),
        RecursiveIteratorIterator::LEAVES_ONLY
    );

    foreach ($files as $name => $file) {
        if (!$file->isDir()) {
            $file_path = $file->getRealPath();
            $relative_path = 'wp-git-interface/' . substr($file_path, strlen($plugin_dir) + 1);
            $zip->addFile($file_path, $relative_path);
        }
    }

    $zip->close();
    
    echo "\n✅ SUCCESS! Plugin ZIP created: $zip_file\n";
    echo "   Size: " . number_format(filesize($zip_file) / 1024, 2) . " KB\n\n";
    echo "Installation:\n";
    echo "1. Upload $zip_file via WordPress Admin > Plugins > Add New > Upload\n";
    echo "2. Or extract to /wp-content/plugins/\n";
    echo "3. Activate the plugin\n";
    echo "4. Create /wp-content/git-repos/ directory\n";
    echo "5. Clone your repositories into it\n";
    echo "6. Add [git_interface] to any page\n\n";
} else {
    echo "❌ Failed to create ZIP file\n";
}

// Clean up temp directory
function rrmdir($dir) {
    if (is_dir($dir)) {
        $objects = scandir($dir);
        foreach ($objects as $object) {
            if ($object != "." && $object != "..") {
                if (is_dir($dir . "/" . $object)) {
                    rrmdir($dir . "/" . $object);
                } else {
                    unlink($dir . "/" . $object);
                }
            }
        }
        rmdir($dir);
    }
}

rrmdir($temp_dir);

echo "Temporary files cleaned up.\n";
echo "=========================================\n";
/**
 * WP Git Interface Plugin ZIP Creator
 * 
 * Run this script: php create-wp-git-plugin-zip.php
 * This will create wp-git-interface.zip with all plugin files
 */

echo "Creating WP Git Interface Plugin ZIP...\n";

// Create temporary directory
$temp_dir = sys_get_temp_dir() . '/wp-git-interface-' . time();
$plugin_dir = $temp_dir . '/wp-git-interface';
mkdir($plugin_dir, 0755, true);
mkdir($plugin_dir . '/includes', 0755, true);
mkdir($plugin_dir . '/assets', 0755, true);

// Main plugin file
$main_plugin_file = '<?php
/**
 * Plugin Name: WP Git Interface
 * Plugin URI: https://example.com/wp-git-interface
 * Description: A Git web interface for WordPress, similar to cgit or GitLab
 * Version: 2.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

if (!defined(\'ABSPATH\')) exit;

define(\'WPGI_PLUGIN_DIR\', plugin_dir_path(__FILE__));
define(\'WPGI_PLUGIN_URL\', plugin_dir_url(__FILE__));
define(\'WPGI_VERSION\', \'2.0.0\');

require_once WPGI_PLUGIN_DIR . \'includes/class-git-operations.php\';

class WPGitInterface {
    
    private static $instance = null;
    private $repos_dir;
    private $git_ops;
    
    public static function getInstance() {
        if (self::$instance == null) {
            self::$instance = new WPGitInterface();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->repos_dir = defined(\'WPGI_REPOS_DIR\') ? WPGI_REPOS_DIR : WP_CONTENT_DIR . \'/git-repos\';
        $this->git_ops = new WPGitOperations($this->repos_dir);
        $this->init();
    }
    
    private function init() {
        if (!file_exists($this->repos_dir)) {
            wp_mkdir_p($this->repos_dir);
            wp_mkdir_p($this->repos_dir . \'/users\');
            wp_mkdir_p($this->repos_dir . \'/gists\');
            file_put_contents($this->repos_dir . \'/.htaccess\', \'Deny from all\');
        }
        
        add_action(\'wp_enqueue_scripts\', array($this, \'enqueue_assets\'));
        add_action(\'admin_menu\', array($this, \'add_admin_menu\'));
        
        add_shortcode(\'git_interface\', array($this, \'render_interface\'));
        add_shortcode(\'git_repo\', array($this, \'render_single_repo\'));
        
        $this->register_ajax_handlers();
    }
    
    private function register_ajax_handlers() {
        $actions = array(
            \'wpgi_get_file_content\',
            \'wpgi_view_file\',
            \'wpgi_view_raw\',
            \'wpgi_download_file\',
            \'wpgi_view_diff\',
            \'wpgi_get_commits\',
            \'wpgi_get_repositories\'
        );
        
        foreach ($actions as $action) {
            add_action(\'wp_ajax_\' . $action, array($this, \'ajax_\' . str_replace(\'wpgi_\', \'\', $action)));
            add_action(\'wp_ajax_nopriv_\' . $action, array($this, \'ajax_\' . str_replace(\'wpgi_\', \'\', $action)));
        }
    }
    
    public function enqueue_assets() {
        global $post;
        if (!is_a($post, \'WP_Post\') || !has_shortcode($post->post_content, \'git_\')) return;
        
        wp_enqueue_style(\'wpgi-fontawesome\', \'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\', array(), \'6.5.1\');
        wp_enqueue_style(\'wpgi-prism\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css\', array(), \'1.29.0\');
        wp_enqueue_style(\'wpgi-prism-line-numbers\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css\', array(), \'1.29.0\');
        wp_enqueue_style(\'wpgi-style\', WPGI_PLUGIN_URL . \'assets/style.css\', array(), WPGI_VERSION);
        
        wp_enqueue_script(\'wpgi-mustache\', \'https://cdnjs.cloudflare.com/ajax/libs/mustache.js/4.2.0/mustache.min.js\', array(), \'4.2.0\', true);
        wp_enqueue_script(\'wpgi-marked\', \'https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js\', array(), \'4.3.0\', true);
        wp_enqueue_script(\'wpgi-prism\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js\', array(), \'1.29.0\', true);
        wp_enqueue_script(\'wpgi-prism-autoloader\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js\', array(\'wpgi-prism\'), \'1.29.0\', true);
        wp_enqueue_script(\'wpgi-prism-line-numbers\', \'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js\', array(\'wpgi-prism\'), \'1.29.0\', true);
        wp_enqueue_script(\'wpgi-script\', WPGI_PLUGIN_URL . \'assets/script.js\', array(\'jquery\', \'wpgi-mustache\', \'wpgi-marked\'), WPGI_VERSION, true);
        
        wp_localize_script(\'wpgi-script\', \'wpgi_ajax\', array(
            \'ajax_url\' => admin_url(\'admin-ajax.php\'),
            \'nonce\' => wp_create_nonce(\'wpgi_nonce\'),
            \'plugin_url\' => WPGI_PLUGIN_URL,
            \'site_url\' => site_url(),
            \'is_logged_in\' => is_user_logged_in()
        ));
    }
    
    public function add_admin_menu() {
        add_menu_page(\'Git Interface\', \'Git Interface\', \'manage_options\', \'wp-git-interface\', array($this, \'admin_page\'), \'dashicons-media-code\', 30);
    }
    
    public function admin_page() {
        $repos = $this->git_ops->get_all_repositories();
        ?>
        <div class="wrap">
            <h1>Git Interface Settings</h1>
            <div class="card">
                <h2>Repository Directory: <code><?php echo esc_html($this->repos_dir); ?></code></h2>
                <h3>Repositories (<?php echo count($repos); ?>):</h3>
                <ul>
                <?php foreach ($repos as $repo): ?>
                    <li><?php echo esc_html($repo); ?></li>
                <?php endforeach; ?>
                </ul>
                <h3>Usage:</h3>
                <p>Add <code>[git_interface]</code> to any page to display repositories.</p>
                <p>Use <code>[git_repo repo="name.git"]</code> to display a specific repository.</p>
            </div>
        </div>
        <?php
    }
    
    public function render_interface($atts) {
        $atts = shortcode_atts(array(\'user\' => \'\', \'view\' => \'grid\'), $atts);
        ob_start();
        ?>
        <div class="wpgi-wrapper wpgi-container">
            <div class="wpgi-header">
                <h2 class="wpgi-title"><i class="fab fa-git-alt"></i> <span>Git Repositories</span></h2>
                <div class="wpgi-controls">
                    <input type="text" class="wpgi-search" placeholder="Search repositories..." />
                </div>
            </div>
            <div class="wpgi-content" id="wpgi-main-content">
                <div class="wpgi-loading"><i class="fas fa-spinner fa-spin"></i> <span>Loading...</span></div>
            </div>
        </div>
        
        <script type="text/template" id="wpgi-repo-list-template">
            <div class="wpgi-repo-grid">
                {{#repos}}
                <div class="wpgi-card wpgi-repo-card" data-repo="{{name}}">
                    <div class="wpgi-repo-header">
                        <i class="fas fa-book"></i>
                        <h3>{{display_name}}</h3>
                    </div>
                    <p class="wpgi-repo-description">{{description}}</p>
                    <button onclick="wpgiLoadRepo(\'{{name}}\')" class="wpgi-view-repo">View</button>
                </div>
                {{/repos}}
            </div>
        </script>
        
        <script>jQuery(document).ready(function() { wpgiLoadRepositories(<?php echo json_encode($atts); ?>); });</script>
        <?php
        return ob_get_clean();
    }
    
    public function render_single_repo($atts) {
        $atts = shortcode_atts(array(\'repo\' => \'\', \'branch\' => \'main\'), $atts);
        if (empty($atts[\'repo\'])) return \'<div class="wpgi-error">No repository specified</div>\';
        
        ob_start();
        ?>
        <div class="wpgi-wrapper wpgi-repo-view" data-repo="<?php echo esc_attr($atts[\'repo\']); ?>">
            <div class="wpgi-repo-container" id="wpgi-repo-<?php echo esc_attr(sanitize_title($atts[\'repo\'])); ?>">
                <div class="wpgi-loading"><i class="fas fa-spinner fa-spin"></i> <span>Loading repository...</span></div>
            </div>
        </div>
        
        <script type="text/template" id="wpgi-repo-view-template">
            <div class="wpgi-repo-header">
                <h2>{{repo_name}}</h2>
            </div>
            <div class="wpgi-repo-nav">
                <button class="wpgi-tab active" data-tab="code"><i class="fas fa-code"></i> Code</button>
                <button class="wpgi-tab" data-tab="commits"><i class="fas fa-history"></i> Commits</button>
            </div>
            <div class="wpgi-repo-content">
                <div class="wpgi-tab-content" id="code-tab">
                    <div class="wpgi-file-browser">
                        <div class="wpgi-breadcrumb">{{#breadcrumb}}<a href="#" onclick="wpgiNavigate(\'{{path}}\')">{{name}}</a>{{#separator}}<span>/</span>{{/separator}}{{/breadcrumb}}</div>
                        <table class="wpgi-file-list">
                            <thead><tr><th>Name</th><th>Last commit</th><th>Last update</th></tr></thead>
                            <tbody>
                                {{#files}}
                                <tr class="wpgi-file-row" onclick="wpgi{{type}}Click(\'{{path}}\')">
                                    <td><i class="{{icon}}"></i> <span>{{name}}</span></td>
                                    <td>{{last_commit}}</td>
                                    <td>{{last_update}}</td>
                                </tr>
                                {{/files}}
                            </tbody>
                        </table>
                    </div>
                    {{#readme}}
                    <div class="wpgi-readme">
                        <div class="wpgi-readme-header"><i class="fas fa-book-open"></i> README.md</div>
                        <div class="wpgi-readme-content wpgi-markdown">{{{readme_html}}}</div>
                    </div>
                    {{/readme}}
                </div>
                <div class="wpgi-tab-content" id="commits-tab" style="display:none;"></div>
            </div>
        </script>
        
        <script type="text/template" id="wpgi-file-viewer-template">
            <div class="wpgi-file-viewer">
                <div class="wpgi-file-header">
                    <div class="wpgi-file-info"><i class="{{icon}}"></i> <span>{{filename}}</span></div>
                    <div class="wpgi-file-actions">
                        <button onclick="wpgiViewRaw(\'{{path}}\')"><i class="fas fa-file-alt"></i> Raw</button>
                        <button onclick="wpgiDownload(\'{{path}}\')"><i class="fas fa-download"></i> Download</button>
                        <button onclick="wpgiCloseViewer()"><i class="fas fa-times"></i></button>
                    </div>
                </div>
                <div class="wpgi-file-content">
                    <pre class="line-numbers"><code class="language-{{language}}">{{{content}}}</code></pre>
                </div>
            </div>
        </script>
        
        <script>jQuery(document).ready(function() { wpgiLoadSingleRepo(<?php echo json_encode($atts); ?>); });</script>
        <?php
        return ob_get_clean();
    }
    
    // AJAX Handlers
    public function ajax_get_repositories() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        $repos = $this->git_ops->get_all_repositories();
        $formatted = array();
        
        foreach ($repos as $repo) {
            $stats = $this->git_ops->get_repo_stats($repo);
            $formatted[] = array(
                \'name\' => $repo,
                \'display_name\' => str_replace(\'.git\', \'\', basename($repo)),
                \'description\' => \'Repository\',
                \'commits\' => $stats[\'commits\'],
                \'branches\' => $stats[\'branches\']
            );
        }
        
        wp_send_json_success(array(\'repos\' => $formatted));
    }
    
    public function ajax_get_file_content() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        
        $repo = sanitize_text_field($_POST[\'repo\'] ?? \'\');
        $path = sanitize_text_field($_POST[\'path\'] ?? \'\');
        $branch = sanitize_text_field($_POST[\'branch\'] ?? \'main\');
        
        if (empty($repo)) wp_send_json_error(\'Repository not specified\');
        
        $files = $this->git_ops->get_file_tree($repo, $branch, $path);
        
        $readme = null;
        if (empty($path)) {
            $readme_content = $this->git_ops->get_file_content($repo, \'README.md\', $branch);
            if ($readme_content) {
                $readme = array(
                    \'content\' => $readme_content,
                    \'html\' => $this->parse_markdown($readme_content)
                );
            }
        }
        
        $breadcrumb = $this->build_breadcrumb($repo, $path);
        $branches = $this->git_ops->get_branches($repo);
        
        wp_send_json_success(array(
            \'files\' => $this->format_files($files),
            \'readme\' => $readme,
            \'breadcrumb\' => $breadcrumb,
            \'branches\' => $branches,
            \'repo_name\' => str_replace(\'.git\', \'\', $repo)
        ));
    }
    
    public function ajax_view_file() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        
        $repo = sanitize_text_field($_POST[\'repo\'] ?? \'\');
        $path = sanitize_text_field($_POST[\'path\'] ?? \'\');
        $branch = sanitize_text_field($_POST[\'branch\'] ?? \'main\');
        
        if (empty($repo) || empty($path)) wp_send_json_error(\'Invalid parameters\');
        
        $content = $this->git_ops->get_file_content($repo, $path, $branch);
        if ($content === false) wp_send_json_error(\'File not found\');
        
        $filename = basename($path);
        $language = $this->git_ops->get_file_language($filename);
        
        wp_send_json_success(array(
            \'filename\' => $filename,
            \'path\' => $path,
            \'content\' => htmlspecialchars($content),
            \'language\' => $language,
            \'icon\' => \'fas fa-file-code\'
        ));
    }
    
    public function ajax_view_raw() {
        $repo = sanitize_text_field($_GET[\'repo\'] ?? \'\');
        $path = sanitize_text_field($_GET[\'path\'] ?? \'\');
        $branch = sanitize_text_field($_GET[\'branch\'] ?? \'main\');
        
        if (empty($repo) || empty($path)) wp_die(\'Invalid parameters\');
        
        $content = $this->git_ops->get_file_content($repo, $path, $branch);
        if ($content === false) wp_die(\'File not found\');
        
        header(\'Content-Type: text/plain\');
        echo $content;
        exit;
    }
    
    public function ajax_download_file() {
        $repo = sanitize_text_field($_GET[\'repo\'] ?? \'\');
        $path = sanitize_text_field($_GET[\'path\'] ?? \'\');
        $branch = sanitize_text_field($_GET[\'branch\'] ?? \'main\');
        
        if (empty($repo) || empty($path)) wp_die(\'Invalid parameters\');
        
        $content = $this->git_ops->get_file_content($repo, $path, $branch);
        if ($content === false) wp_die(\'File not found\');
        
        header(\'Content-Type: application/octet-stream\');
        header(\'Content-Disposition: attachment; filename="\' . basename($path) . \'"\');
        echo $content;
        exit;
    }
    
    public function ajax_view_diff() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        
        $repo = sanitize_text_field($_POST[\'repo\'] ?? \'\');
        $commit = sanitize_text_field($_POST[\'commit\'] ?? \'\');
        
        if (empty($repo) || empty($commit)) wp_send_json_error(\'Invalid parameters\');
        
        $diff_data = $this->git_ops->get_commit_diff($repo, $commit);
        if (!$diff_data) wp_send_json_error(\'Commit not found\');
        
        $formatted_diff = $this->format_diff($diff_data[\'diff\']);
        
        wp_send_json_success(array(
            \'commit\' => $diff_data[\'commit\'],
            \'stats\' => $diff_data[\'stats\'],
            \'diff_html\' => $formatted_diff
        ));
    }
    
    public function ajax_get_commits() {
        check_ajax_referer(\'wpgi_nonce\', \'nonce\');
        
        $repo = sanitize_text_field($_POST[\'repo\'] ?? \'\');
        $branch = sanitize_text_field($_POST[\'branch\'] ?? \'main\');
        
        if (empty($repo)) wp_send_json_error(\'Repository not specified\');
        
        $commits = $this->git_ops->get_commits($repo, $branch, 50);
        wp_send_json_success($commits);
    }
    
    private function format_files($files) {
        $formatted = array();
        foreach ($files as $file) {
            $icon = $file[\'type\'] === \'directory\' ? \'fas fa-folder\' : \'fas fa-file\';
            $formatted[] = array(
                \'name\' => $file[\'name\'],
                \'path\' => $file[\'path\'],
                \'type\' => $file[\'type\'] === \'directory\' ? \'Folder\' : \'File\',
                \'icon\' => $icon,
                \'last_commit\' => $file[\'last_commit\'][\'message\'] ?? \'\',
                \'last_update\' => $file[\'last_commit\'][\'date\'] ?? \'\'
            );
        }
        return $formatted;
    }
    
    private function format_diff($diff) {
        if (empty($diff)) return \'<div class="wpgi-no-diff">No changes</div>\';
        
        $lines = explode("\\n", $diff);
        $html = \'<div class="wpgi-diff-viewer"><pre class="language-diff line-numbers"><code>\';
        
        foreach ($lines as $line) {
            if (strpos($line, \'+++\') === 0) {
                $html .= \'<span class="wpgi-diff-file-new">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } elseif (strpos($line, \'---\') === 0) {
                $html .= \'<span class="wpgi-diff-file-old">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } elseif (strpos($line, \'@@\') === 0) {
                $html .= \'<span class="wpgi-diff-hunk">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } elseif (strpos($line, \'+\') === 0) {
                $html .= \'<span class="wpgi-diff-added">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } elseif (strpos($line, \'-\') === 0) {
                $html .= \'<span class="wpgi-diff-removed">\' . htmlspecialchars($line) . \'</span>\' . "\\n";
            } else {
                $html .= htmlspecialchars($line) . "\\n";
            }
        }
        
        $html .= \'</code></pre></div>\';
        return $html;
    }
    
    private function build_breadcrumb($repo, $path) {
        $breadcrumb = array(array(\'name\' => str_replace(\'.git\', \'\', $repo), \'path\' => \'\', \'separator\' => !empty($path)));
        
        if (!empty($path)) {
            $parts = explode(\'/\', $path);
            $current_path = \'\';
            foreach ($parts as $i => $part) {
                $current_path .= ($i > 0 ? \'/\' : \'\') . $part;
                $breadcrumb[] = array(\'name\' => $part, \'path\' => $current_path, \'separator\' => $i < count($parts) - 1);
            }
        }
        
        return $breadcrumb;
    }
    
    private function parse_markdown($content) {
        // Basic markdown parsing
        $html = htmlspecialchars($content);
        $html = preg_replace(\'/^### (.*?)$/m\', \'<h3>$1</h3>\', $html);
        $html = preg_replace(\'/^## (.*?)$/m\', \'<h2>$1</h2>\', $html);
        $html = preg_replace(\'/^# (.*?)$/m\', \'<h1>$1</h1>\', $html);
        $html = preg_replace(\'/\\*\\*\\*(.*?)\\*\\*\\*/s\', \'<strong><em>$1</em></strong>\', $html);
        $html = preg_replace(\'/\\*\\*(.*?)\\*\\*/s\', \'<strong>$1</strong>\', $html);
        $html = preg_replace(\'/\\*(.*?)\\*/s\', \'<em>$1</em>\', $html);
        $html = preg_replace(\'/\\[([^\\]]+)\\]\\(([^\\)]+)\\)/\', \'<a href="$2">$1</a>\', $html);
        $html = preg_replace(\'/```([a-z]*)\\n(.*?)```/s\', \'<pre><code class="language-$1">$2</code></pre>\', $html);
        $html = preg_replace(\'/`([^`]+)`/\', \'<code>$1</code>\', $html);
        $html = nl2br($html);
        return $html;
    }
}

add_action(\'plugins_loaded\', function() { WPGitInterface::getInstance(); });

register_activation_hook(__FILE__, function() {
    $repos_dir = defined(\'WPGI_REPOS_DIR\') ? WPGI_REPOS_DIR : WP_CONTENT_DIR . \'/git-repos\';
    if (!file_exists($repos_dir)) {
        wp_mkdir_p($repos_dir);
        file_put_contents($repos_dir . \'/.htaccess\', \'Deny from all\');
    }
    flush_rewrite_rules();
});

register_deactivation_hook(__FILE__, function() { flush_rewrite_rules(); });
';

file_put_contents($plugin_dir . '/wp-git-interface.php', $main_plugin_file);

// Git Operations Class
$git_operations_file = '<?php 
class WPGitOperations {
    
    private $repos_dir;
    
    public function __construct($repos_dir) {
        $this->repos_dir = $repos_dir;
    }
    
    private function exec_git($repo, $command, $args = array()) {
        $repo_path = $this->repos_dir . \'/\' . $repo;
        if (!file_exists($repo_path)) return false;
        
        $cmd = \'cd \' . escapeshellarg($repo_path) . \' && git \' . $command;
        if (!empty($args)) {
            foreach ($args as $arg) {
                $cmd .= \' \' . escapeshellarg($arg);
            }
        }
        
        $output = array();
        $return_var = 0;
        exec($cmd . \' 2>&1\', $output, $return_var);
        
        return array(\'success\' => $return_var === 0, \'output\' => $output, \'return_code\' => $return_var);
    }
    
    public function get_file_tree($repo, $branch = \'HEAD\', $path = \'\') {
        $tree_path = $branch;
        if (!empty($path)) $tree_path .= \':\' . $path;
        
        $result = $this->exec_git($repo, \'ls-tree\', array(\'--full-name\', $tree_path));
        if (!$result[\'success\']) return array();
        
        $files = array();
        foreach ($result[\'output\'] as $line) {
            if (empty($line)) continue;
            
            if (preg_match(\'/^(\\d+)\\s+(blob|tree)\\s+([0-9a-f]+)\\s+(.+)$/\', $line, $matches)) {
                $file_path = $matches[4];
                $file_name = basename($file_path);
                $is_dir = $matches[2] === \'tree\';
                
                $file_info = $this->get_file_info($repo, $file_path, $branch);
                
                $files[] = array(
                    \'name\' => $file_name,
                    \'path\' => $file_path,
                    \'type\' => $is_dir ? \'directory\' : \'file\',
                    \'size\' => $is_dir ? null : $file_info[\'size\'],
                    \'last_commit\' => $file_info[\'last_commit\']
                );
            }
        }
        
        usort($files, function($a, $b) {
            if ($a[\'type\'] === $b[\'type\']) return strcasecmp($a[\'name\'], $b[\'name\']);
            return $a[\'type\'] === \'directory\' ? -1 : 1;
        });
        
        return $files;
    }
    
    public function get_file_content($repo, $path, $branch = \'HEAD\') {
        $result = $this->exec_git($repo, \'show\', array($branch . \':\' . $path));
        if ($result[\'success\']) return implode("\\n", $result[\'output\']);
        return false;
    }
    
    /**
     * Get file info
     */
    public function get_file_info($repo, $path, $branch = \'HEAD\') {
        $info = array(
            \'size\' => 0,
            \'last_commit\' => array(
                \'hash\' => \'\',
                \'message\' => \'\',
                \'date\' => \'\'
            )
        );
        
        // Get file size (for blobs only)
        $size_result = $this->exec_git($repo, \'cat-file\', array(\'-s\', $branch . \':\' . $path));
        if ($size_result[\'success\'] && !empty($size_result[\'output\'])) {
            $info[\'size\'] = $this->format_file_size(intval($size_result[\'output\'][0]));
        }
        
        // Get last commit for this file
        $log_result = $this->exec_git($repo, \'log\', array(
            \'-1\',
            \'--pretty=format:%H|%s|%at\',
            $branch,
            \'--\',
            $path
        ));
        
        if ($log_result[\'success\'] && !empty($log_result[\'output\'])) {
            $parts = explode(\'|\', $log_result[\'output\'][0], 3);
            if (count($parts) >= 3) {
                $info[\'last_commit\'] = array(
                    \'hash\' => substr($parts[0], 0, 7),
                    \'message\' => $this->truncate_message($parts[1]),
                    \'date\' => $this->time_elapsed_string($parts[2])
                );
            }
        }
        
        return $info;
    }
    
    /**
     * Get commits
     */
    public function get_commits($repo, $branch = \'HEAD\', $limit = 50, $skip = 0) {
        $result = $this->exec_git($repo, \'log\', array(
            \'--pretty=format:%H|%an|%ae|%at|%s|%b\',
            \'--max-count=\' . $limit,
            \'--skip=\' . $skip,
            $branch
        ));
        
        if (!$result[\'success\']) {
            return array();
        }
        
        $commits = array();
        foreach ($result[\'output\'] as $line) {
            if (empty($line)) continue;
            
            $parts = explode(\'|\', $line, 6);
            if (count($parts) >= 5) {
                $commits[] = array(
                    \'hash\' => $parts[0],
                    \'short_hash\' => substr($parts[0], 0, 7),
                    \'author\' => $parts[1],
                    \'email\' => $parts[2],
                    \'timestamp\' => $parts[3],
                    \'date\' => date(\'Y-m-d H:i:s\', $parts[3]),
                    \'relative_date\' => $this->time_elapsed_string($parts[3]),
                    \'subject\' => $parts[4],
                    \'body\' => isset($parts[5]) ? $parts[5] : \'\'
                );
            }
        }
        
        return $commits;
    }
    
    /**
     * Get branches
     */
    public function get_branches($repo) {
        // Get all branches with their info
        $result = $this->exec_git($repo, \'for-each-ref\', array(
            \'--format=%(refname:short)|%(committerdate:unix)|%(subject)\',
            \'refs/heads/\'
        ));
        
        if (!$result[\'success\']) {
            return array();
        }
        
        $branches = array();
        $current_branch = $this->get_current_branch($repo);
        
        foreach ($result[\'output\'] as $line) {
            if (empty($line)) continue;
            
            $parts = explode(\'|\', $line, 3);
            if (count($parts) >= 2) {
                $branch_name = $parts[0];
                $branches[] = array(
                    \'name\' => $branch_name,
                    \'is_current\' => ($branch_name === $current_branch),
                    \'last_commit_date\' => isset($parts[1]) ? date(\'Y-m-d H:i:s\', $parts[1]) : \'\',
                    \'relative_date\' => isset($parts[1]) ? $this->time_elapsed_string($parts[1]) : \'\',
                    \'last_commit_message\' => isset($parts[2]) ? $parts[2] : \'\'
                );
            }
        }
        
        return $branches;
    }
    
    /**
     * Get current branch
     */
    public function get_current_branch($repo) {
        $result = $this->exec_git($repo, \'symbolic-ref\', array(\'--short\', \'HEAD\'));
        
        if ($result[\'success\'] && !empty($result[\'output\'])) {
            return trim($result[\'output\'][0]);
        }
        
        // Try to get from HEAD file directly
        $head_file = $this->repos_dir . \'/\' . $repo . \'/HEAD\';
        if (file_exists($head_file)) {
            $head = file_get_contents($head_file);
            if (preg_match(\'/ref: refs\/heads\/(.+)/\', $head, $matches)) {
                return trim($matches[1]);
            }
        }
        
        return \'main\';
    }
    
    /**
     * Get commit diff with proper formatting
     */
    public function get_commit_diff($repo, $commit_hash) {
        // Get commit info
        $info_result = $this->exec_git($repo, \'show\', array(
            \'--pretty=format:%H|%an|%ae|%at|%s|%b\',
            \'--no-patch\',
            $commit_hash
        ));
        
        $commit_info = array();
        if ($info_result[\'success\'] && !empty($info_result[\'output\'])) {
            $parts = explode(\'|\', $info_result[\'output\'][0], 6);
            if (count($parts) >= 5) {
                $commit_info = array(
                    \'hash\' => $parts[0],
                    \'short_hash\' => substr($parts[0], 0, 7),
                    \'author\' => $parts[1],
                    \'email\' => $parts[2],
                    \'date\' => date(\'Y-m-d H:i:s\', $parts[3]),
                    \'relative_date\' => $this->time_elapsed_string($parts[3]),
                    \'subject\' => $parts[4],
                    \'body\' => isset($parts[5]) ? $parts[5] : \'\'
                );
            }
        }
        
        // Get file stats
        $stats_result = $this->exec_git($repo, \'show\', array(
            \'--stat\',
            \'--no-patch\',
            $commit_hash
        ));
        
        $stats = \'\';
        if ($stats_result[\'success\']) {
            $stats = implode("\n", array_slice($stats_result[\'output\'], 7)); // Skip commit info
        }
        
        // Get the actual diff
        $diff_result = $this->exec_git($repo, \'show\', array(
            \'--patch\',
            \'--no-prefix\',
            $commit_hash
        ));
        
        $diff = \'\';
        if ($diff_result[\'success\']) {
            // Find where the diff starts (after the commit message)
            $diff_lines = $diff_result[\'output\'];
            $start_index = 0;
            foreach ($diff_lines as $i => $line) {
                if (strpos($line, \'diff --git\') === 0) {
                    $start_index = $i;
                    break;
                }
            }
            $diff = implode("\n", array_slice($diff_lines, $start_index));
        }
        
        return array(
            \'commit\' => $commit_info,
            \'stats\' => $stats,
            \'diff\' => $diff
        );
    }
    
    /**
     * Get repository statistics
     */
    public function get_repo_stats($repo) {
        $stats = array(
            \'commits\' => 0,
            \'branches\' => 0,
            \'tags\' => 0,
            \'contributors\' => 0,
            \'files\' => 0,
            \'size\' => \'0 KB\'
        );
        
        // Count commits
        $result = $this->exec_git($repo, \'rev-list\', array(\'--count\', \'--all\'));
        if ($result[\'success\'] && !empty($result[\'output\'])) {
            $stats[\'commits\'] = intval($result[\'output\'][0]);
        }
        
        // Count branches
        $branches = $this->get_branches($repo);
        $stats[\'branches\'] = count($branches);
        
        // Count tags
        $tags = $this->get_tags($repo);
        $stats[\'tags\'] = count($tags);
        
        // Count contributors
        $result = $this->exec_git($repo, \'shortlog\', array(\'-sn\', \'HEAD\'));
        if ($result[\'success\']) {
            $stats[\'contributors\'] = count($result[\'output\']);
        }
        
        // Count files
        $result = $this->exec_git($repo, \'ls-files\', array());
        if ($result[\'success\']) {
            $stats[\'files\'] = count($result[\'output\']);
        }
        
        // Get repository size
        $repo_path = $this->repos_dir . \'/\' . $repo;
        $stats[\'size\'] = $this->get_directory_size($repo_path);
        
        return $stats;
    }
    
    /**
     * Get tags
     */
    public function get_tags($repo) {
        $result = $this->exec_git($repo, \'tag\', array(
            \'-l\',
            \'--format=%(refname:short)|%(creatordate:unix)|%(subject)\'
        ));
        
        if (!$result[\'success\']) {
            return array();
        }
        
        $tags = array();
        foreach ($result[\'output\'] as $line) {
            if (empty($line)) continue;
            
            $parts = explode(\'|\', $line, 3);
            if (!empty($parts[0])) {
                $tags[] = array(
                    \'name\' => $parts[0],
                    \'date\' => isset($parts[1]) && is_numeric($parts[1]) ? 
                             date(\'Y-m-d H:i:s\', $parts[1]) : \'\',
                    \'relative_date\' => isset($parts[1]) && is_numeric($parts[1]) ? 
                                      $this->time_elapsed_string($parts[1]) : \'\',
                    \'message\' => isset($parts[2]) ? $parts[2] : \'\'
                );
            }
        }
        
        return array_reverse($tags);
    }
    
    /**
     * Search in repository
     */
    public function search_in_repo($repo, $query, $branch = \'HEAD\') {
        $result = $this->exec_git($repo, \'grep\', array(
            \'-n\',
            \'-i\',
            \'--break\',
            \'--heading\',
            $query,
            $branch
        ));
        
        if (!$result[\'success\']) {
            return array();
        }
        
        $results = array();
        $current_file = \'\';
        
        foreach ($result[\'output\'] as $line) {
            if (empty($line)) continue;
            
            if (strpos($line, \'--\') === 0) {
                continue;
            } elseif (strpos($line, $branch) === 0) {
                // New file
                $current_file = substr($line, strlen($branch) + 1);
                if (!isset($results[$current_file])) {
                    $results[$current_file] = array();
                }
            } elseif ($current_file && preg_match(\'/^(\d+)[:-](.*)$/\', $line, $matches)) {
                $results[$current_file][] = array(
                    \'line_number\' => $matches[1],
                    \'content\' => $matches[2]
                );
            }
        }
        
        return $results;
    }
    
    /**
     * Search all repositories
     */
    public function search_all_repos($query) {
        $results = array();
        $repos = $this->get_all_repositories();
        
        foreach ($repos as $repo) {
            $repo_results = $this->search_in_repo($repo, $query);
            if (!empty($repo_results)) {
                $results[$repo] = $repo_results;
            }
        }
        
        return $results;
    }
    
    /**
     * Get all repositories
     */
    public function get_all_repositories() {
        $repos = array();
        
        // Get root level repos
        if (is_dir($this->repos_dir)) {
            $dirs = scandir($this->repos_dir);
            foreach ($dirs as $dir) {
                if ($dir != \'.\' && $dir != \'..\' && $dir != \'users\' && $dir != \'gists\') {
                    if ($this->is_git_repo($this->repos_dir . \'/\' . $dir)) {
                        $repos[] = $dir;
                    }
                }
            }
        }
        
        // Get user repos
        $users_dir = $this->repos_dir . \'/users\';
        if (is_dir($users_dir)) {
            $users = scandir($users_dir);
            foreach ($users as $user) {
                if ($user != \'.\' && $user != \'..\') {
                    $user_repos_dir = $users_dir . \'/\' . $user;
                    if (is_dir($user_repos_dir)) {
                        $user_repos = scandir($user_repos_dir);
                        foreach ($user_repos as $repo) {
                            if ($repo != \'.\' && $repo != \'..\') {
                                if ($this->is_git_repo($user_repos_dir . \'/\' . $repo)) {
                                    $repos[] = \'users/\' . $user . \'/\' . $repo;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return $repos;
    }
    
    /**
     * Check if directory is a git repository
     */
    private function is_git_repo($path) {
        return file_exists($path . \'/HEAD\') || 
               file_exists($path . \'/.git/HEAD\') ||
               file_exists($path . \'/config\');
    }
    
    /**
     * Get file language for syntax highlighting
     */
    public function get_file_language($filename) {
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        $languages = array(
            \'php\' => \'php\',
            \'js\' => \'javascript\',
            \'jsx\' => \'jsx\',
            \'ts\' => \'typescript\',
            \'tsx\' => \'tsx\',
            \'py\' => \'python\',
            \'rb\' => \'ruby\',
            \'java\' => \'java\',
            \'c\' => \'c\',
            \'cpp\' => \'cpp\',
            \'cs\' => \'csharp\',
            \'go\' => \'go\',
            \'rs\' => \'rust\',
            \'swift\' => \'swift\',
            \'kt\' => \'kotlin\',
            \'sh\' => \'bash\',
            \'bash\' => \'bash\',
            \'html\' => \'html\',
            \'xml\' => \'xml\',
            \'css\' => \'css\',
            \'scss\' => \'scss\',
            \'sass\' => \'sass\',
            \'json\' => \'json\',
            \'yaml\' => \'yaml\',
            \'yml\' => \'yaml\',
            \'sql\' => \'sql\',
            \'md\' => \'markdown\',
            \'dockerfile\' => \'docker\',
            \'makefile\' => \'makefile\'
        );
        
        // Check special filenames
        $filename_lower = strtolower($filename);
        if ($filename_lower === \'dockerfile\') {
            return \'docker\';
        } elseif ($filename_lower === \'makefile\') {
            return \'makefile\';
        }
        
        return $languages[$ext] ?? \'plaintext\';
    }
    
    // Helper methods
    
    private function format_file_size($bytes) {
        if ($bytes == 0) return \'0 B\';
        
        $k = 1024;
        $sizes = array(\'B\', \'KB\', \'MB\', \'GB\');
        $i = floor(log($bytes) / log($k));
        
        return round($bytes / pow($k, $i), 2) . \' \' . $sizes[$i];
    }
    
    private function get_directory_size($path) {
        $bytes = 0;
        
        if (is_dir($path)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($path, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $bytes += $file->getSize();
                }
            }
        }
        
        return $this->format_file_size($bytes);
    }
    
    private function time_elapsed_string($timestamp) {
        if (!is_numeric($timestamp)) {
            return \'Unknown\';
        }
        
        $time = time() - $timestamp;
        
        if ($time < 1) {
            return \'just now\';
        }
        
        $tokens = array(
            31536000 => \'year\',
            2592000 => \'month\',
            604800 => \'week\',
            86400 => \'day\',
            3600 => \'hour\',
            60 => \'minute\',
            1 => \'second\'
        );
        
        foreach ($tokens as $unit => $text) {
            if ($time < $unit) continue;
            $numberOfUnits = floor($time / $unit);
            return $numberOfUnits . \' \' . $text . (($numberOfUnits > 1) ? \'s\' : \'\') . \' ago\';
        }
    }
    
    private function truncate_message($message, $length = 50) {
        if (strlen($message) <= $length) {
            return $message;
        }
        return substr($message, 0, $length - 3) . \'...\';
    }
}
'; 