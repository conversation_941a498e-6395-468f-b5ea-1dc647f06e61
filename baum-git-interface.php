<?php
/**
 * Plugin Name: WP Git Interface
 * Plugin URI: https://example.com/wp-git-interface
 * Description: A Git web interface for WordPress, similar to cgit or GitLab
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WPGI_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WPGI_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WPGI_VERSION', '1.0.0');

// Include Git Operations class
require_once WPGI_PLUGIN_DIR . 'includes/class-git-operations.php';

// Main plugin class
class WPGitInterface {
    
    private static $instance = null;
    private $repos_dir;
    private $git_ops;
    
    public static function getInstance() {
        if (self::$instance == null) {
            self::$instance = new WPGitInterface();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->repos_dir = defined('WPGI_REPOS_DIR') ? WPGI_REPOS_DIR : WP_CONTENT_DIR . '/git-repos';
        $this->git_ops = new WPGitOperations($this->repos_dir);
        $this->init();
    }
    
    private function init() {
        // Create repos directory if it doesn't exist
        if (!file_exists($this->repos_dir)) {
            wp_mkdir_p($this->repos_dir);
            file_put_contents($this->repos_dir . '/.htaccess', 'Deny from all');
        }
        
        // Hooks
        add_action('init', array($this, 'register_rewrite_rules'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_shortcode('git_interface', array($this, 'render_interface'));
        
        // AJAX handlers
        add_action('wp_ajax_wpgi_get_file_content', array($this, 'ajax_get_file_content'));
        add_action('wp_ajax_nopriv_wpgi_get_file_content', array($this, 'ajax_get_file_content'));
        add_action('wp_ajax_wpgi_browse_folder', array($this, 'ajax_browse_folder'));
        add_action('wp_ajax_nopriv_wpgi_browse_folder', array($this, 'ajax_browse_folder'));
        add_action('wp_ajax_wpgi_view_file', array($this, 'ajax_view_file'));
        add_action('wp_ajax_nopriv_wpgi_view_file', array($this, 'ajax_view_file'));
        add_action('wp_ajax_wpgi_search_repo', array($this, 'ajax_search_repo'));
        add_action('wp_ajax_nopriv_wpgi_search_repo', array($this, 'ajax_search_repo'));
        add_action('wp_ajax_wpgi_view_commit', array($this, 'ajax_view_commit'));
        add_action('wp_ajax_nopriv_wpgi_view_commit', array($this, 'ajax_view_commit'));
        add_action('wp_ajax_wpgi_refresh_stats', array($this, 'ajax_refresh_stats'));
        add_action('wp_ajax_nopriv_wpgi_refresh_stats', array($this, 'ajax_refresh_stats'));
    }
    
    public function register_rewrite_rules() {
        add_rewrite_rule('^git/([^/]+)/?', 'index.php?wpgi_repo=$matches[1]', 'top');
        add_rewrite_rule('^git/([^/]+)/tree/(.+)?', 'index.php?wpgi_repo=$matches[1]&wpgi_action=tree&wpgi_path=$matches[2]', 'top');
        add_rewrite_rule('^git/([^/]+)/blob/(.+)?', 'index.php?wpgi_repo=$matches[1]&wpgi_action=blob&wpgi_path=$matches[2]', 'top');
        add_rewrite_rule('^git/([^/]+)/commits/?', 'index.php?wpgi_repo=$matches[1]&wpgi_action=commits', 'top');
        
        add_filter('query_vars', function($vars) {
            $vars[] = 'wpgi_repo';
            $vars[] = 'wpgi_action';
            $vars[] = 'wpgi_path';
            return $vars;
        });
    }
    
    public function enqueue_assets() {
        if (is_page() && has_shortcode(get_post()->post_content, 'git_interface')) {
            // Tailwind CSS via CDN script (not stylesheet)
            wp_register_script('tailwindcss', 'https://cdn.tailwindcss.com', array(), null, false);
            wp_add_inline_script('tailwindcss', 'window.tailwind = window.tailwind || {}; window.tailwind.config = { darkMode: "media", corePlugins: { preflight: false }, important: ".wpgi-container" };', 'before');
            wp_enqueue_script('tailwindcss');

            // Font Awesome 6
            wp_enqueue_style('fontawesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css', array(), '6.5.1');
            
            // Prism.js for syntax highlighting
            wp_enqueue_style('prismjs', 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css', array(), '1.29.0');
            wp_enqueue_script('prismjs', 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js', array(), '1.29.0', true);
            wp_enqueue_script('prismjs-autoloader', 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js', array('prismjs'), '1.29.0', true);
            
            // Custom styles
            wp_enqueue_style('wpgi-style', WPGI_PLUGIN_URL . 'assets/style.css', array(), WPGI_VERSION);
            
            // Custom JS
            wp_enqueue_script('wpgi-script', WPGI_PLUGIN_URL . 'assets/script.js', array('jquery'), WPGI_VERSION, true);
            wp_localize_script('wpgi-script', 'wpgi_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wpgi_nonce')
            ));
        }
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Git Interface',
            'Git Interface',
            'manage_options',
            'wp-git-interface',
            array($this, 'admin_page'),
            'dashicons-media-code',
            30
        );
    }
    
    public function admin_page() {
        $repos = $this->get_repositories();
        ?>
        <div class="wrap">
            <h1><i class="dashicons dashicons-media-code"></i> Git Interface Settings</h1>
            
            <div class="card">
                <h2>Repository Management</h2>
                <p>Repository Directory: <code><?php echo esc_html($this->repos_dir); ?></code></p>
                
                <h3>Current Repositories (<?php echo count($repos); ?>)</h3>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Repository</th>
                            <th>Description</th>
                            <th>Last Updated</th>
                            <th>Size</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php
                    if (empty($repos)) {
                        echo '<tr><td colspan="4">No repositories found. Add repositories to the directory above.</td></tr>';
                    } else {
                        foreach ($repos as $repo) {
                            $info = $this->get_repository_info($repo);
                            $stats = $this->git_ops->get_repo_stats($repo);
                            echo '<tr>';
                            echo '<td><strong>' . esc_html(str_replace('.git', '', $repo)) . '</strong></td>';
                            echo '<td>' . esc_html($info['description'] ?: 'No description') . '</td>';
                            echo '<td>' . esc_html($info['last_commit']) . '</td>';
                            echo '<td>' . esc_html($stats['size']) . '</td>';
                            echo '</tr>';
                        }
                    }
                    ?>
                    </tbody>
                </table>
            </div>
            
            <div class="card">
                <h2>Usage Instructions</h2>
                <ol>
                    <li>Add the shortcode <code>[git_interface]</code> to any page</li>
                    <li>Clone repositories to: <code><?php echo esc_html($this->repos_dir); ?></code></li>
                    <li>Use bare repositories for better performance: <code>git clone --bare URL</code></li>
                </ol>
                
                <h3>Clone a Repository</h3>
                <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
cd <?php echo esc_html($this->repos_dir); ?>

# Clone a public repository
git clone --bare https://github.com/username/repository.git

# Clone with custom name
git clone --bare https://github.com/username/repository.git custom-name.git

# Mirror repository (for syncing)
git clone --mirror https://github.com/username/repository.git</pre>
            </div>
            
            <div class="card">
                <h2>Shortcode Options</h2>
                <table class="form-table">
                    <tr>
                        <th>Basic Usage</th>
                        <td><code>[git_interface]</code></td>
                    </tr>
                    <tr>
                        <th>Specific Repository</th>
                        <td><code>[git_interface repo="repository.git"]</code></td>
                    </tr>
                    <tr>
                        <th>Custom View</th>
                        <td><code>[git_interface view="list"]</code> (options: grid, list)</td>
                    </tr>
                </table>
            </div>
        </div>
        <?php
    }
    
    public function render_interface($atts) {
        $atts = shortcode_atts(array(
            'repo' => '',
            'view' => 'grid'
        ), $atts);
        
        ob_start();
        ?>
        <div class="wpgi-container bg-gray-50 p-4 rounded-lg">
            <div class="bg-white rounded-lg shadow-lg">
                <!-- Header -->
                <div class="bg-gradient-to-r from-gray-800 to-gray-700 text-white p-4 rounded-t-lg">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-git-alt text-3xl"></i>
                            <div>
                                <h1 class="text-2xl font-bold">Git Repositories</h1>
                                <p class="text-gray-300 text-sm">Browse and explore code repositories</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <input type="text" id="wpgi-repo-search" placeholder="Search repositories..." 
                                   class="px-3 py-1 rounded text-gray-800 text-sm">
                            <button id="wpgi-refresh-repos" class="bg-gray-600 hover:bg-gray-500 px-3 py-1 rounded text-sm transition">
                                <i class="fas fa-sync-alt mr-1"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Repository List -->
                <div class="p-6">
                    <?php 
                    if (!empty($atts['repo'])) {
                        $this->render_single_repository($atts['repo']);
                    } else {
                        $this->render_repository_list($atts['view']);
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <!-- Repository View Modal -->
        <div id="wpgi-repo-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 wpgi-modal-overlay">
            <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-7xl shadow-lg rounded-md bg-white wpgi-modal-content">
                <div id="wpgi-repo-content">
                    <!-- Dynamic content loaded here -->
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    private function render_repository_list($view = 'grid') {
        $repos = $this->get_repositories();
        
        if (empty($repos)) {
            ?>
            <div class="text-center py-12">
                <i class="fas fa-inbox text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">No Repositories Found</h3>
                <p class="text-gray-500">Add repositories to <code class="bg-gray-100 px-2 py-1 rounded"><?php echo esc_html($this->repos_dir); ?></code></p>
            </div>
            <?php
            return;
        }
        
        $view_class = $view === 'list' ? 'space-y-4' : 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
        ?>
        <div class="<?php echo esc_attr($view_class); ?>">
            <?php foreach ($repos as $repo): ?>
                <?php 
                $repo_info = $this->get_repository_info($repo);
                $stats = $this->git_ops->get_repo_stats($repo);
                ?>
                <div class="wpgi-repo-card border border-gray-200 rounded-lg p-5 hover:shadow-xl transition-all duration-300 cursor-pointer bg-white" 
                     onclick="wpgiOpenRepo('<?php echo esc_js($repo); ?>')"
                     data-repo="<?php echo esc_attr($repo); ?>">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center">
                            <i class="fas fa-book text-blue-500 text-xl mr-3"></i>
                            <div>
                                <h3 class="font-bold text-lg text-gray-800 wpgi-repo-name">
                                    <?php echo esc_html(str_replace('.git', '', $repo)); ?>
                                </h3>
                                <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                    <i class="fas fa-code-branch mr-1"></i><?php echo esc_html($repo_info['branch']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <p class="text-gray-600 text-sm mb-4 wpgi-repo-desc">
                        <?php echo esc_html($repo_info['description'] ?: 'No description available'); ?>
                    </p>
                    
                    <div class="grid grid-cols-3 gap-2 text-xs text-gray-500">
                        <div class="flex items-center">
                            <i class="fas fa-code-branch mr-1 text-gray-400"></i>
                            <span class="wpgi-branch-count"><?php echo esc_html($stats['branches']); ?></span> branches
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-history mr-1 text-gray-400"></i>
                            <span class="wpgi-commit-count"><?php echo esc_html($stats['commits']); ?></span> commits
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-users mr-1 text-gray-400"></i>
                            <span><?php echo esc_html($stats['contributors']); ?></span> contributors
                        </div>
                    </div>
                    
                    <div class="mt-3 pt-3 border-t border-gray-100 flex justify-between items-center">
                        <span class="text-xs text-gray-400 wpgi-last-update">
                            <i class="far fa-clock mr-1"></i>Updated <?php echo esc_html($repo_info['last_commit']); ?>
                        </span>
                        <span class="text-xs text-gray-400">
                            <i class="fas fa-database mr-1"></i><?php echo esc_html($stats['size']); ?>
                        </span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    private function render_single_repository($repo) {
        // Render a single repository view directly
        $this->render_repository_browser($repo);
    }
    
    private function get_repositories() {
        $repos = array();
        if (is_dir($this->repos_dir)) {
            $dirs = scandir($this->repos_dir);
            foreach ($dirs as $dir) {
                if ($dir != '.' && $dir != '..' && $dir != '.htaccess' && is_dir($this->repos_dir . '/' . $dir)) {
                    // Check if it's a git repository
                    if (file_exists($this->repos_dir . '/' . $dir . '/HEAD') || 
                        file_exists($this->repos_dir . '/' . $dir . '/.git/HEAD')) {
                        $repos[] = $dir;
                    }
                }
            }
        }
        return apply_filters('wpgi_repositories', $repos);
    }
    
    private function get_repository_info($repo) {
        $repo_path = $this->repos_dir . '/' . $repo;
        $info = array(
            'description' => '',
            'branch' => 'main',
            'branches' => 0,
            'last_commit' => 'Unknown'
        );
        
        // Get description
        $desc_file = $repo_path . '/description';
        if (file_exists($desc_file)) {
            $desc = file_get_contents($desc_file);
            if ($desc && strpos($desc, 'Unnamed repository') === false) {
                $info['description'] = trim($desc);
            }
        }
        
        // Get current branch
        $info['branch'] = $this->git_ops->get_current_branch($repo);
        
        // Get branches count
        $branches = $this->git_ops->get_branches($repo);
        $info['branches'] = count($branches);
        
        // Get last commit date
        $commits = $this->git_ops->get_commits($repo, 'HEAD', 1);
        if (!empty($commits)) {
            $info['last_commit'] = $commits[0]['relative_date'];
        }
        
        return $info;
    }
    
    public function ajax_get_file_content() {
        check_ajax_referer('wpgi_nonce', 'nonce');
        
        $repo = sanitize_text_field($_POST['repo']);
        $action = sanitize_text_field($_POST['action_type']);
        
        // Check permissions
        if (!apply_filters('wpgi_can_view_repo', true, $repo)) {
            wp_send_json_error('Access denied');
            return;
        }
        
        ob_start();
        
        switch ($action) {
            case 'browse':
                $this->render_repository_browser($repo);
                break;
            case 'commits':
                $this->render_commit_list($repo);
                break;
            case 'branches':
                $this->render_branch_list($repo);
                break;
            case 'tags':
                $this->render_tag_list($repo);
                break;
            default:
                echo 'Invalid action';
        }
        
        $content = ob_get_clean();
        wp_send_json_success($content);
    }
    
    public function ajax_browse_folder() {
        check_ajax_referer('wpgi_nonce', 'nonce');
        
        $repo = sanitize_text_field($_POST['repo']);
        $path = sanitize_text_field($_POST['path']);
        $branch = sanitize_text_field($_POST['branch']);
        
        ob_start();
        $this->render_file_tree_content($repo, $branch, $path);
        $content = ob_get_clean();
        
        wp_send_json_success($content);
    }
    
    public function ajax_view_file() {
        check_ajax_referer('wpgi_nonce', 'nonce');
        
        $repo = sanitize_text_field($_POST['repo']);
        $path = sanitize_text_field($_POST['path']);
        $branch = sanitize_text_field($_POST['branch']);
        
        $content = $this->git_ops->get_file_content($repo, $path, $branch);
        $language = $this->git_ops->get_file_language(basename($path));
        
        ob_start();
        ?>
        <div class="wpgi-code-container">
            <div class="wpgi-code-header">
                <span class="filename"><i class="fas fa-file-code mr-2"></i><?php echo esc_html(basename($path)); ?></span>
                <div class="space-x-2">
                    <button onclick="wpgiViewRaw('<?php echo esc_js($repo); ?>', '<?php echo esc_js($path); ?>')" 
                            class="text-gray-400 hover:text-white text-sm">
                        <i class="fas fa-file-alt mr-1"></i>Raw
                    </button>
                    <button onclick="wpgiDownloadFile('<?php echo esc_js($repo); ?>', '<?php echo esc_js($path); ?>')" 
                            class="text-gray-400 hover:text-white text-sm">
                        <i class="fas fa-download mr-1"></i>Download
                    </button>
                    <button onclick="$(this).closest('.fixed').remove()" class="text-gray-400 hover:text-white text-sm">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <pre class="line-numbers"><code class="language-<?php echo esc_attr($language); ?>"><?php echo esc_html($content); ?></code></pre>
        </div>
        <?php
        $html = ob_get_clean();
        
        wp_send_json_success($html);
    }
    
    public function ajax_search_repo() {
        check_ajax_referer('wpgi_nonce', 'nonce');
        
        $repo = sanitize_text_field($_POST['repo']);
        $query = sanitize_text_field($_POST['query']);
        $branch = sanitize_text_field($_POST['branch']);
        
        $results = $this->git_ops->search_in_repo($repo, $query, $branch);
        
        ob_start();
        if (empty($results)) {
            echo '<p class="text-gray-500">No results found for "' . esc_html($query) . '"</p>';
        } else {
            foreach ($results as $file => $matches) {
                ?>
                <div class="wpgi-search-result mb-4 p-3">
                    <h4 class="font-semibold text-blue-600 mb-2">
                        <i class="fas fa-file-code mr-1"></i><?php echo esc_html($file); ?>
                    </h4>
                    <?php foreach ($matches as $match): ?>
                        <div class="text-sm text-gray-700 ml-4">
                            Line <?php echo esc_html($match['line_number']); ?>: 
                            <code class="bg-gray-100 px-1"><?php echo esc_html($match['content']); ?></code>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php
            }
        }
        $html = ob_get_clean();
        
        wp_send_json_success($html);
    }
    
    public function ajax_view_commit() {
        check_ajax_referer('wpgi_nonce', 'nonce');
        
        $repo = sanitize_text_field($_POST['repo']);
        $commit = sanitize_text_field($_POST['commit']);
        
        $diff = $this->git_ops->get_commit_diff($repo, $commit);
        
        ob_start();
        ?>
        <div class="wpgi-commit-viewer">
            <div class="bg-gray-100 p-4 rounded-t-lg">
                <h3 class="text-lg font-semibold mb-2">
                    Commit <?php echo esc_html(substr($diff['commit']['hash'], 0, 7)); ?>
                </h3>
                <div class="text-sm text-gray-600">
                    <div><i class="fas fa-user mr-1"></i> <?php echo esc_html($diff['commit']['author']); ?></div>
                    <div><i class="fas fa-calendar mr-1"></i> <?php echo esc_html($diff['commit']['date']); ?></div>
                    <div class="mt-2 font-medium text-gray-800"><?php echo esc_html($diff['commit']['subject']); ?></div>
                </div>
            </div>
            <div class="p-4">
                <pre class="wpgi-diff-viewer bg-gray-900 text-gray-100 p-4 rounded overflow-x-auto"><?php echo esc_html($diff['diff']); ?></pre>
            </div>
        </div>
        <?php
        $html = ob_get_clean();
        
        wp_send_json_success($html);
    }
    
    public function ajax_refresh_stats() {
        check_ajax_referer('wpgi_nonce', 'nonce');
        
        $repo = sanitize_text_field($_POST['repo']);
        $stats = $this->git_ops->get_repo_stats($repo);
        $info = $this->get_repository_info($repo);
        
        wp_send_json_success(array(
            'branches' => $stats['branches'],
            'commits' => $stats['commits'],
            'lastUpdate' => $info['last_commit']
        ));
    }
    
    private function render_repository_browser($repo) {
        $branches = $this->git_ops->get_branches($repo);
        $current_branch = $this->git_ops->get_current_branch($repo);
        $stats = $this->git_ops->get_repo_stats($repo);
        ?>
        <div class="bg-gray-50 rounded-lg">
            <!-- Repository Header -->
            <div class="bg-white border-b px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <button onclick="wpgiCloseModal()" class="text-gray-500 hover:text-gray-700 transition">
                            <i class="fas fa-arrow-left text-lg"></i>
                        </button>
                        <i class="fas fa-book text-gray-600 text-xl"></i>
                        <h2 class="text-2xl font-bold text-gray-800"><?php echo esc_html(str_replace('.git', '', $repo)); ?></h2>
                    </div>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button onclick="wpgiGetCloneUrl('<?php echo esc_js($repo); ?>', 'https')" 
                                    class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition text-sm">
                                <i class="fas fa-download mr-2"></i>Clone
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Repository Stats -->
            <div class="bg-white border-b px-6 py-3">
                <div class="flex space-x-6 text-sm">
                    <span class="text-gray-600">
                        <i class="fas fa-history text-gray-400 mr-1"></i>
                        <strong><?php echo number_format($stats['commits']); ?></strong> commits
                    </span>
                    <span class="text-gray-600">
                        <i class="fas fa-code-branch text-gray-400 mr-1"></i>
                        <strong><?php echo $stats['branches']; ?></strong> branches
                    </span>
                    <span class="text-gray-600">
                        <i class="fas fa-tag text-gray-400 mr-1"></i>
                        <strong><?php echo $stats['tags']; ?></strong> tags
                    </span>
                    <span class="text-gray-600">
                        <i class="fas fa-users text-gray-400 mr-1"></i>
                        <strong><?php echo $stats['contributors']; ?></strong> contributors
                    </span>
                </div>
            </div>
            
            <!-- Navigation Tabs -->
            <div class="bg-white border-b">
                <div class="flex space-x-8 px-6">
                    <button class="wpgi-tab py-3 border-b-2 border-blue-500 text-blue-600 font-medium" data-tab="code">
                        <i class="fas fa-code mr-2"></i>Code
                    </button>
                    <button onclick="wpgiLoadCommits('<?php echo esc_js($repo); ?>')" 
                            class="wpgi-tab py-3 text-gray-600 hover:text-gray-900 transition" data-tab="commits">
                        <i class="fas fa-history mr-2"></i>Commits
                    </button>
                    <button onclick="wpgiLoadBranches('<?php echo esc_js($repo); ?>')" 
                            class="wpgi-tab py-3 text-gray-600 hover:text-gray-900 transition" data-tab="branches">
                        <i class="fas fa-code-branch mr-2"></i>Branches
                    </button>
                    <button onclick="wpgiLoadTags('<?php echo esc_js($repo); ?>')" 
                            class="wpgi-tab py-3 text-gray-600 hover:text-gray-900 transition" data-tab="tags">
                        <i class="fas fa-tag mr-2"></i>Tags
                    </button>
                </div>
            </div>
            
            <!-- File Browser -->
            <div class="bg-white rounded-b-lg">
                <div class="p-6">
                    <div class="mb-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3 text-sm text-gray-600">
                                <i class="fas fa-code-branch"></i>
                                <select id="wpgi-branch-select" class="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:border-blue-500">
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo esc_attr($branch['name']); ?>" 
                                                <?php echo $branch['is_current'] ? 'selected' : ''; ?>>
                                            <?php echo esc_html($branch['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <span class="text-gray-400">/</span>
                                <nav class="wpgi-breadcrumb">
                                    <a href="#" class="text-blue-600"><?php echo esc_html($repo); ?></a>
                                </nav>
                            </div>
                            <div class="flex items-center space-x-2">
                                <input type="text" id="wpgi-file-filter" placeholder="Filter files..." 
                                       class="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:border-blue-500">
                                <input type="text" id="wpgi-search-input" placeholder="Search in repository..." 
                                       class="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:border-blue-500">
                            </div>
                        </div>
                    </div>
                    
                    <!-- File Tree -->
                    <div id="wpgi-file-browser" class="border border-gray-200 rounded-lg overflow-hidden">
                        <?php $this->render_file_tree_content($repo, $current_branch, ''); ?>
                    </div>
                </div>
                
                <!-- README -->
                <?php
                $readme_content = $this->git_ops->get_file_content($repo, 'README.md', $current_branch);
                if ($readme_content):
                ?>
                <div class="p-6 border-t">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="font-bold text-lg mb-3">
                            <i class="fas fa-book-open mr-2 text-gray-600"></i>README.md
                        </h3>
                        <div class="prose prose-sm max-w-none">
                            <pre class="whitespace-pre-wrap text-gray-700"><?php echo esc_html($readme_content); ?></pre>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    private function render_file_tree_content($repo, $branch, $path) {
        $files = $this->git_ops->get_file_tree($repo, $branch, $path);
        
        if (empty($files)) {
            ?>
            <div class="p-8 text-center text-gray-500">
                <i class="fas fa-folder-open text-4xl mb-3"></i>
                <p>This directory is empty</p>
            </div>
            <?php
            return;
        }
        ?>
        <table class="w-full">
            <thead class="bg-gray-50 border-b">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Commit</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Update</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach ($files as $file): ?>
                    <?php 
                    $icon = $this->get_file_icon($file['name'], $file['type']);
                    $is_dir = $file['type'] === 'directory';
                    ?>
                    <tr class="wpgi-file-row hover:bg-gray-50 transition">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="wpgi-file-icon <?php echo $is_dir ? 'text-blue-500' : 'text-gray-400'; ?>">
                                <i class="<?php echo esc_attr($icon); ?>"></i>
                            </span>
                            <?php if ($is_dir): ?>
                                <a href="#" onclick="wpgiNavigateToFolder('<?php echo esc_js($repo); ?>', '<?php echo esc_js($file['path']); ?>')" 
                                   class="wpgi-filename text-blue-600 hover:underline ml-2 font-medium">
                                    <?php echo esc_html($file['name']); ?>
                                </a>
                            <?php else: ?>
                                <a href="#" onclick="wpgiViewFile('<?php echo esc_js($repo); ?>', '<?php echo esc_js($file['path']); ?>')" 
                                   class="wpgi-filename text-blue-600 hover:underline ml-2">
                                    <?php echo esc_html($file['name']); ?>
                                </a>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            <?php echo esc_html($file['last_commit']['message']); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?php echo esc_html($file['last_commit']['date']); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?php echo $is_dir ? '-' : esc_html($file['size']); ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }
    
    private function get_file_icon($filename, $type) {
        if ($type === 'directory') {
            return 'fas fa-folder';
        }
        
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        $icons = array(
            // Code files
            'php' => 'fab fa-php',
            'js' => 'fab fa-js-square',
            'jsx' => 'fab fa-react',
            'ts' => 'fab fa-js-square',
            'tsx' => 'fab fa-react',
            'py' => 'fab fa-python',
            'java' => 'fab fa-java',
            'c' => 'fas fa-file-code',
            'cpp' => 'fas fa-file-code',
            'h' => 'fas fa-file-code',
            'cs' => 'fas fa-file-code',
            'go' => 'fas fa-file-code',
            'rb' => 'fas fa-gem',
            'swift' => 'fab fa-swift',
            
            // Web files
            'html' => 'fab fa-html5',
            'htm' => 'fab fa-html5',
            'css' => 'fab fa-css3-alt',
            'scss' => 'fab fa-sass',
            'sass' => 'fab fa-sass',
            'less' => 'fab fa-less',
            
            // Data files
            'json' => 'fas fa-file-code',
            'xml' => 'fas fa-file-code',
            'yaml' => 'fas fa-file-code',
            'yml' => 'fas fa-file-code',
            'sql' => 'fas fa-database',
            
            // Documents
            'md' => 'fab fa-markdown',
            'txt' => 'fas fa-file-alt',
            'pdf' => 'fas fa-file-pdf',
            'doc' => 'fas fa-file-word',
            'docx' => 'fas fa-file-word',
            
            // Images
            'jpg' => 'fas fa-file-image',
            'jpeg' => 'fas fa-file-image',
            'png' => 'fas fa-file-image',
            'gif' => 'fas fa-file-image',
            'svg' => 'fas fa-file-image',
            
            // Archives
            'zip' => 'fas fa-file-archive',
            'tar' => 'fas fa-file-archive',
            'gz' => 'fas fa-file-archive',
            'rar' => 'fas fa-file-archive',
            
            // Config files
            'env' => 'fas fa-cog',
            'ini' => 'fas fa-cog',
            'conf' => 'fas fa-cog',
            'config' => 'fas fa-cog',
            
            // Version control
            'gitignore' => 'fab fa-git',
            'gitmodules' => 'fab fa-git',
            
            // Docker
            'dockerfile' => 'fab fa-docker',
            'dockerignore' => 'fab fa-docker'
        );
        
        $icon = isset($icons[$ext]) ? $icons[$ext] : 'fas fa-file';
        
        // Check for specific filenames
        if ($filename === '.gitignore' || $filename === '.gitmodules') {
            $icon = 'fab fa-git';
        } elseif ($filename === 'Dockerfile') {
            $icon = 'fab fa-docker';
        } elseif ($filename === 'package.json') {
            $icon = 'fab fa-npm';
        } elseif ($filename === 'composer.json') {
            $icon = 'fab fa-php';
        }
        
        return apply_filters('wpgi_file_icon', $icon, $filename);
    }
    
    private function render_commit_list($repo) {
        $commits = $this->git_ops->get_commits($repo, 'HEAD', 50);
        ?>
        <div class="bg-white rounded-lg">
            <!-- Header -->
            <div class="border-b px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold">
                        <i class="fas fa-history mr-2 text-gray-600"></i>Commit History
                    </h3>
                    <button onclick="wpgiBackToBrowse('<?php echo esc_js($repo); ?>')" 
                            class="text-blue-600 hover:text-blue-800 transition text-sm">
                        <i class="fas fa-arrow-left mr-1"></i> Back to files
                    </button>
                </div>
            </div>
            
            <!-- Commits -->
            <div class="wpgi-commit-list">
                <?php foreach ($commits as $commit): ?>
                <div class="wpgi-commit-item border-b hover:bg-gray-50 p-4 transition">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <p class="font-semibold text-gray-900 mb-1">
                                <?php echo esc_html($commit['subject']); ?>
                            </p>
                            <?php if (!empty($commit['body'])): ?>
                                <p class="text-sm text-gray-600 mb-2"><?php echo esc_html($commit['body']); ?></p>
                            <?php endif; ?>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span>
                                    <i class="fas fa-user-circle mr-1"></i>
                                    <?php echo esc_html($commit['author']); ?>
                                </span>
                                <span>
                                    <i class="fas fa-clock mr-1"></i>
                                    <?php echo esc_html($commit['relative_date']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="text-right ml-4">
                            <button onclick="wpgiViewCommit('<?php echo esc_js($repo); ?>', '<?php echo esc_js($commit['hash']); ?>')" 
                                    class="wpgi-commit-hash hover:bg-gray-200 transition">
                                <?php echo esc_html(substr($commit['hash'], 0, 7)); ?>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }
    
    private function render_branch_list($repo) {
        $branches = $this->git_ops->get_branches($repo);
        ?>
        <div class="bg-white rounded-lg">
            <!-- Header -->
            <div class="border-b px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold">
                        <i class="fas fa-code-branch mr-2 text-gray-600"></i>Branches
                    </h3>
                    <button onclick="wpgiBackToBrowse('<?php echo esc_js($repo); ?>')" 
                            class="text-blue-600 hover:text-blue-800 transition text-sm">
                        <i class="fas fa-arrow-left mr-1"></i> Back to files
                    </button>
                </div>
            </div>
            
            <!-- Branch List -->
            <div class="p-6">
                <div class="space-y-3">
                    <?php foreach ($branches as $branch): ?>
                    <div class="wpgi-branch-item <?php echo $branch['is_current'] ? 'active' : ''; ?> 
                                flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition">
                        <div class="flex items-center">
                            <?php if ($branch['is_current']): ?>
                                <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <?php else: ?>
                                <i class="fas fa-code-branch text-gray-400 mr-3"></i>
                            <?php endif; ?>
                            <div>
                                <span class="font-semibold"><?php echo esc_html($branch['name']); ?></span>
                                <?php if ($branch['is_current']): ?>
                                    <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">default</span>
                                <?php endif; ?>
                                <div class="text-sm text-gray-600 mt-1">
                                    <?php echo esc_html($branch['last_commit_message']); ?>
                                </div>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">
                            Updated <?php echo esc_html($branch['relative_date']); ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function render_tag_list($repo) {
        $tags = $this->git_ops->get_tags($repo);
        ?>
        <div class="bg-white rounded-lg">
            <!-- Header -->
            <div class="border-b px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold">
                        <i class="fas fa-tag mr-2 text-gray-600"></i>Tags
                    </h3>
                    <button onclick="wpgiBackToBrowse('<?php echo esc_js($repo); ?>')" 
                            class="text-blue-600 hover:text-blue-800 transition text-sm">
                        <i class="fas fa-arrow-left mr-1"></i> Back to files
                    </button>
                </div>
            </div>
            
            <!-- Tag List -->
            <div class="p-6">
                <?php if (empty($tags)): ?>
                    <p class="text-gray-500 text-center py-8">No tags found in this repository</p>
                <?php else: ?>
                    <div class="space-y-3">
                        <?php foreach ($tags as $tag): ?>
                        <div class="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition">
                            <div class="flex items-center">
                                <i class="fas fa-tag text-yellow-500 mr-3"></i>
                                <div>
                                    <span class="font-semibold"><?php echo esc_html($tag['name']); ?></span>
                                    <?php if (!empty($tag['message'])): ?>
                                        <div class="text-sm text-gray-600 mt-1">
                                            <?php echo esc_html($tag['message']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <span class="text-sm text-gray-500">
                                <?php echo esc_html($tag['relative_date']); ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
}

// Initialize plugin
add_action('plugins_loaded', function() {
    WPGitInterface::getInstance();
});

// Activation hook
register_activation_hook(__FILE__, function() {
    // Create repository directory
    $repos_dir = defined('WPGI_REPOS_DIR') ? WPGI_REPOS_DIR : WP_CONTENT_DIR . '/git-repos';
    if (!file_exists($repos_dir)) {
        wp_mkdir_p($repos_dir);
        file_put_contents($repos_dir . '/.htaccess', 'Deny from all');
    }
    
    // Flush rewrite rules
    flush_rewrite_rules();
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    // Flush rewrite rules
    flush_rewrite_rules();
});