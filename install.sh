#!/bin/bash

# WP Git Interface Plugin Installation Script
# Run this from your WordPress plugins directory

echo "========================================="
echo "WP Git Interface Plugin Installation"
echo "========================================="

# Plugin directory name
PLUGIN_DIR="wp-git-interface"

# Check if we're in the plugins directory
if [[ ! "$PWD" == *"/wp-content/plugins"* ]]; then
    echo "❌ Error: Please run this script from your WordPress plugins directory"
    echo "   Example: cd /var/www/html/wp-content/plugins"
    exit 1
fi

# Check if Git is installed
if ! command -v git &> /dev/null; then
    echo "❌ Error: Git is not installed. Please install Git first."
    echo "   Ubuntu/Debian: sudo apt-get install git"
    echo "   CentOS/RHEL: sudo yum install git"
    exit 1
fi

# Create plugin directory structure
echo "📁 Creating plugin directory structure..."
mkdir -p "$PLUGIN_DIR"
mkdir -p "$PLUGIN_DIR/assets"
mkdir -p "$PLUGIN_DIR/includes"

# Create the main plugin file
echo "📝 Creating main plugin file..."
cat > "$PLUGIN_DIR/wp-git-interface.php" << 'MAINFILE'
# [Insert the content from wp-git-main-plugin-file artifact here]
MAINFILE

# Create the Git operations class
echo "📝 Creating Git operations class..."
cat > "$PLUGIN_DIR/includes/class-git-operations.php" << 'GITOPS'
# [Insert the content from git-operations-class-file artifact here]
GITOPS

# Create the JavaScript file
echo "📝 Creating JavaScript file..."
cat > "$PLUGIN_DIR/assets/script.js" << 'JAVASCRIPT'
# [Insert the content from wp-git-interface-js artifact here]
JAVASCRIPT

# Create the CSS file
echo "📝 Creating CSS file..."
cat > "$PLUGIN_DIR/assets/style.css" << 'STYLES'
# [Insert the content from wp-git-interface-css artifact here]
STYLES

# Create the repository directory
echo "📁 Creating repository storage directory..."
REPO_DIR="../../git-repos"
mkdir -p "$REPO_DIR"

# Set proper permissions
echo "🔒 Setting permissions..."
chmod 755 "$PLUGIN_DIR"
chmod 755 "$PLUGIN_DIR/assets"
chmod 755 "$PLUGIN_DIR/includes"
chmod 644 "$PLUGIN_DIR"/*.php
chmod 644 "$PLUGIN_DIR/assets"/*
chmod 644 "$PLUGIN_DIR/includes"/*
chmod 755 "$REPO_DIR"

# Create .htaccess for security
echo "🔐 Creating security .htaccess..."
echo "Deny from all" > "$REPO_DIR/.htaccess"

# Get web server user
WEB_USER="www-data"
if [ -f /etc/redhat-release ]; then
    WEB_USER="apache"
fi

# Set ownership (requires sudo)
echo ""
echo "⚠️  To complete installation, run these commands with sudo:"
echo ""
echo "sudo chown -R $WEB_USER:$WEB_USER $REPO_DIR"
echo "sudo chown -R $WEB_USER:$WEB_USER $PLUGIN_DIR"
echo ""

# Create a sample repository (optional)
read -p "Would you like to clone a sample repository? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📦 Cloning WordPress repository as sample..."
    cd "$REPO_DIR"
    git clone --bare https://github.com/WordPress/WordPress-Coding-Standards.git sample.git
    cd - > /dev/null
    echo "✅ Sample repository cloned"
fi

echo ""
echo "========================================="
echo "✅ Installation Complete!"
echo "========================================="
echo ""
echo "Next steps:"
echo "1. Activate the plugin in WordPress admin"
echo "2. Add repositories to: $(realpath $REPO_DIR)"
echo "3. Add [git_interface] shortcode to any page"
echo "4. Visit the page to see your repositories"
echo ""
echo "Clone repositories using:"
echo "cd $(realpath $REPO_DIR)"
echo "git clone --bare https://github.com/username/repo.git"
echo ""
echo "Thank you for installing WP Git Interface!"
echo "========================================="

