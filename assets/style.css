.wpgi-wrapper {
    --wpgi-primary: var(--color-primary, #000);
    --wpgi-accent: var(--color-accent, #0066cc);
    --wpgi-success: var(--color-green, #28a745);
    --wpgi-danger: var(--color-red, #dc3545);
    --wpgi-border-radius: var(--border-radius, 8px);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.wpgi-wrapper.wpgi-container {
    background: transparent !important;
    min-height: 400px;
}

.wpgi-wrapper .wpgi-card {
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.wpgi-wrapper .wpgi-repo-card {
    background: white !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: var(--wpgi-border-radius) !important;
    padding: 1.25rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.wpgi-wrapper .wpgi-repo-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    border-color: var(--wpgi-accent) !important;
}

.wpgi-wrapper .wpgi-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 2rem !important;
    padding: 1.5rem !important;
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
}

.wpgi-wrapper .wpgi-title {
    font-size: 1.75rem !important;
    font-weight: 700 !important;
    color: var(--wpgi-primary) !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.wpgi-wrapper .wpgi-repo-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)) !important;
    gap: 1.5rem !important;
}

.wpgi-wrapper .wpgi-file-browser {
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
    overflow: hidden !important;
}

.wpgi-wrapper .wpgi-file-list {
    width: 100% !important;
    border-collapse: collapse !important;
}

.wpgi-wrapper .wpgi-file-list th {
    padding: 0.75rem 1rem !important;
    text-align: left !important;
    font-weight: 600 !important;
    background: #f7f7f7 !important;
}

.wpgi-wrapper .wpgi-file-list td {
    padding: 0.75rem 1rem !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.wpgi-wrapper .wpgi-file-row:hover {
    background-color: #fafafa !important;
}

.wpgi-wrapper .wpgi-file-viewer {
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
    margin-top: 1.5rem !important;
    overflow: hidden !important;
}

.wpgi-wrapper pre[class*="language-"].line-numbers {
    position: relative !important;
    padding-left: 3.8em !important;
    counter-reset: linenumber !important;
}

.wpgi-wrapper .wpgi-diff-added {
    background-color: var(--wpgi-success) !important;
    color: white !important;
    display: block !important;
    padding: 0.125rem 0.5rem !important;
}

.wpgi-wrapper .wpgi-diff-removed {
    background-color: var(--wpgi-danger) !important;
    color: white !important;
    display: block !important;
    padding: 0.125rem 0.5rem !important;
}

.wpgi-wrapper .wpgi-loading {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 3rem !important;
    color: var(--wpgi-accent) !important;
    gap: 0.75rem !important;
}

.wpgi-wrapper .wpgi-loading i {
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.wpgi-wrapper .wpgi-readme {
    margin-top: 2rem !important;
    background: white !important;
    border-radius: var(--wpgi-border-radius) !important;
}

.wpgi-wrapper .wpgi-readme-header {
    background: #f7f7f7 !important;
    padding: 1rem 1.5rem !important;
    font-weight: 600 !important;
}

.wpgi-wrapper .wpgi-markdown h1 {
    font-size: 2rem !important;
    margin: 1.5rem 0 1rem !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 1px solid #e0e0e0 !important;
}

.wpgi-wrapper .wpgi-markdown h2 {
    font-size: 1.5rem !important;
    margin: 1.25rem 0 0.75rem !important;
}

.wpgi-wrapper .wpgi-markdown code {
    background: #f7f7f7 !important;
    padding: 0.125rem 0.375rem !important;
    border-radius: 3px !important;
}

.wpgi-wrapper .wpgi-markdown pre {
    background: #2d2d30 !important;
    padding: 1rem !important;
    border-radius: var(--wpgi-border-radius) !important;
    overflow-x: auto !important;
}

.wpgi-wrapper .wpgi-markdown a {
    color: var(--wpgi-accent) !important;
    text-decoration: none !important;
}

.wpgi-wrapper .wpgi-tab {
    padding: 1rem 1.5rem !important;
    background: none !important;
    border: none !important;
    border-bottom: 2px solid transparent !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
}

.wpgi-wrapper .wpgi-tab.active {
    color: var(--wpgi-accent) !important;
    border-bottom-color: var(--wpgi-accent) !important;
}

.wpgi-wrapper .wpgi-commit-item {
    padding: 1rem !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: var(--wpgi-border-radius) !important;
    margin-bottom: 0.75rem !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
}

.wpgi-wrapper .wpgi-commit-item:hover {
    border-color: var(--wpgi-accent) !important;
    background: #fafafa !important;
}

@media (max-width: 768px) {
    .wpgi-wrapper .wpgi-repo-grid {
        grid-template-columns: 1fr !important;
    }
}