(function($) {
    "use strict";

    window.WPGI = window.WPGI || {};
    
    WPGI.currentRepo = null;
    WPGI.currentPath = "";
    WPGI.currentBranch = "main";
    WPGI.openViewers = [];
    
    window.wpgiLoadRepositories = function(options) {
        showLoading("#wpgi-main-content");
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_get_repositories",
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                renderTemplate("wpgi-repo-list-template", response.data, "#wpgi-main-content");
            }
        });
    };
    
    window.wpgiLoadSingleRepo = function(options) {
        WPGI.currentRepo = options.repo;
        WPGI.currentBranch = options.branch || "main";
        
        const container = "#wpgi-repo-" + options.repo.replace(/[^a-zA-Z0-9]/g, "-");
        showLoading(container);
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_get_file_content",
            repo: options.repo,
            path: "",
            branch: WPGI.currentBranch,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                const data = response.data;
                data.repo_name = options.repo;
                renderTemplate("wpgi-repo-view-template", data, container);
                initializeTabs();
                if (data.readme) {
                    setTimeout(function() {
                        Prism.highlightAllUnder($(container)[0]);
                    }, 100);
                }
            }
        });
    };
    
    window.wpgiLoadRepo = function(repo) {
        closeAllViewers();
        const $card = $('.wpgi-repo-card[data-repo="' + repo + '"]');
        
        if (!$card.next(".wpgi-repo-expanded").length) {
            $card.after('<div class="wpgi-repo-expanded" id="repo-expanded-' + repo.replace(/[^a-zA-Z0-9]/g, "-") + '"></div>');
        }
        
        const container = "#repo-expanded-" + repo.replace(/[^a-zA-Z0-9]/g, "-");
        
        wpgiLoadSingleRepo({
            repo: repo,
            container: container
        });
        
        $("html, body").animate({
            scrollTop: $(container).offset().top - 100
        }, 500);
    };
    
    window.wpgiNavigate = function(path) {
        loadFileTree(WPGI.currentRepo, path, WPGI.currentBranch);
    };
    
    window.wpgiFolderClick = function(path) {
        loadFileTree(WPGI.currentRepo, path, WPGI.currentBranch);
    };
    
    window.wpgiFileClick = function(path) {
        closeAllViewers();
        showLoading(".wpgi-file-viewer-container", "Loading file...");
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_view_file",
            repo: WPGI.currentRepo,
            path: path,
            branch: WPGI.currentBranch,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                if (!$(".wpgi-file-viewer-container").length) {
                    $(".wpgi-file-browser").after('<div class="wpgi-file-viewer-container"></div>');
                }
                
                renderTemplate("wpgi-file-viewer-template", response.data, ".wpgi-file-viewer-container");
                WPGI.openViewers.push(".wpgi-file-viewer-container");
                
                setTimeout(function() {
                    Prism.highlightAllUnder($(".wpgi-file-viewer-container")[0]);
                }, 100);
            }
        });
    };
    
    window.wpgiViewRaw = function(path) {
        const url = wpgi_ajax.ajax_url + "?action=wpgi_view_raw&repo=" + 
                   encodeURIComponent(WPGI.currentRepo) + "&path=" + encodeURIComponent(path) + 
                   "&branch=" + WPGI.currentBranch + "&nonce=" + wpgi_ajax.nonce;
        window.open(url, "_blank");
    };
    
    window.wpgiDownload = function(path) {
        const url = wpgi_ajax.ajax_url + "?action=wpgi_download_file&repo=" + 
                   encodeURIComponent(WPGI.currentRepo) + "&path=" + encodeURIComponent(path) + 
                   "&branch=" + WPGI.currentBranch + "&nonce=" + wpgi_ajax.nonce;
        window.location.href = url;
    };
    
    window.wpgiCloseViewer = function() {
        $(".wpgi-file-viewer-container").fadeOut(function() {
            $(this).remove();
        });
    };
    
    window.wpgiViewCommit = function(repo, hash) {
        showLoading(".wpgi-commit-viewer", "Loading commit...");
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_view_diff",
            repo: repo,
            commit: hash,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                const viewer = $('<div class="wpgi-commit-viewer wpgi-card">' +
                    '<div class="wpgi-commit-header">' +
                    '<button onclick="wpgiCloseCommit()" class="wpgi-btn-back">' +
                    '<i class="fas fa-arrow-left"></i> Back</button>' +
                    '<h3>Commit ' + response.data.commit.short_hash + '</h3></div>' +
                    '<div class="wpgi-commit-info">' +
                    '<div><i class="fas fa-user"></i> ' + response.data.commit.author + '</div>' +
                    '<div><i class="fas fa-calendar"></i> ' + response.data.commit.relative_date + '</div>' +
                    '<div class="wpgi-commit-message">' + response.data.commit.subject + '</div></div>' +
                    '<div class="wpgi-commit-diff">' + response.data.diff_html + '</div></div>');
                
                $(".wpgi-tab-content:visible").html(viewer);
                
                setTimeout(function() {
                    Prism.highlightAllUnder(viewer[0]);
                }, 100);
            }
        });
    };
    
    window.wpgiCloseCommit = function() {
        wpgiLoadCommits(WPGI.currentRepo);
    };
    
    window.wpgiLoadCommits = function(repo) {
        const container = "#commits-tab";
        showLoading(container);
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_get_commits",
            repo: repo || WPGI.currentRepo,
            branch: WPGI.currentBranch,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                renderCommits(response.data, container);
            }
        });
    };
    
    function loadFileTree(repo, path, branch) {
        WPGI.currentPath = path;
        showLoading(".wpgi-file-browser");
        
        $.post(wpgi_ajax.ajax_url, {
            action: "wpgi_get_file_content",
            repo: repo,
            path: path,
            branch: branch,
            nonce: wpgi_ajax.nonce
        }, function(response) {
            if (response.success) {
                const html = renderFileList(response.data.files);
                $(".wpgi-file-list tbody").html(html);
                
                const breadcrumbHtml = renderBreadcrumb(response.data.breadcrumb);
                $(".wpgi-breadcrumb").html(breadcrumbHtml);
                
                if (response.data.readme) {
                    $(".wpgi-readme-content").html(response.data.readme.html);
                    $(".wpgi-readme").show();
                } else {
                    $(".wpgi-readme").hide();
                }
            }
        });
    }
    
    function renderFileList(files) {
        let html = "";
        files.forEach(function(file) {
            const clickHandler = file.type === "Folder" ? "wpgiFolderClick" : "wpgiFileClick";
            html += '<tr class="wpgi-file-row" onclick="' + clickHandler + '(\'' + file.path + '\')">';
            html += '<td><i class="' + file.icon + '"></i> <span>' + file.name + '</span></td>';
            html += '<td>' + (file.last_commit || "-") + '</td>';
            html += '<td>' + (file.last_update || "-") + '</td>';
            html += '</tr>';
        });
        return html || '<tr><td colspan="3" class="wpgi-empty">No files found</td></tr>';
    }
    
    function renderBreadcrumb(breadcrumb) {
        let html = "";
        breadcrumb.forEach(function(item, index) {
            if (index > 0) html += '<span class="wpgi-separator">/</span>';
            html += '<a href="#" onclick="wpgiNavigate(\'' + item.path + '\')">' + item.name + '</a>';
        });
        return html;
    }
    
    function renderCommits(commits, container) {
        let html = '<div class="wpgi-commits-list">';
        commits.forEach(function(commit) {
            html += '<div class="wpgi-commit-item" onclick="wpgiViewCommit(\'' + 
                    WPGI.currentRepo + '\', \'' + commit.hash + '\')">';
            html += '<div class="wpgi-commit-header">';
            html += '<span class="wpgi-commit-hash">' + commit.short_hash + '</span>';
            html += '<span class="wpgi-commit-date">' + commit.relative_date + '</span></div>';
            html += '<div class="wpgi-commit-message">' + commit.subject + '</div>';
            html += '<div class="wpgi-commit-author"><i class="fas fa-user"></i> ' + commit.author + '</div>';
            html += '</div>';
        });
        html += '</div>';
        $(container).html(html);
    }
    
    function renderTemplate(templateId, data, container) {
        const template = $("#" + templateId).html();
        if (template && window.Mustache) {
            const rendered = Mustache.render(template, data);
            $(container).html(rendered);
        }
    }
    
    function showLoading(container, message) {
        const msg = message || "Loading...";
        $(container).html('<div class="wpgi-loading"><i class="fas fa-spinner fa-spin"></i> <span>' + msg + '</span></div>');
    }
    
    function closeAllViewers() {
        WPGI.openViewers.forEach(function(viewer) {
            $(viewer).remove();
        });
        WPGI.openViewers = [];
        $(".wpgi-repo-expanded").remove();
    }
    
    function initializeTabs() {
        $(".wpgi-tab").on("click", function() {
            const tab = $(this).data("tab");
            
            $(".wpgi-tab").removeClass("active");
            $(this).addClass("active");
            
            $(".wpgi-tab-content").hide();
            $("#" + tab + "-tab").show();
            
            if (tab === "commits" && $("#commits-tab").is(":empty")) {
                wpgiLoadCommits(WPGI.currentRepo);
            }
        });
    }
    
    $(document).ready(function() {
        $(".wpgi-search").on("keyup", function() {
            const term = $(this).val().toLowerCase();
            $(".wpgi-repo-card").each(function() {
                const name = $(this).find("h3").text().toLowerCase();
                const desc = $(this).find(".wpgi-repo-description").text().toLowerCase();
                
                if (name.includes(term) || desc.includes(term)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });
        
        $(document).on("keydown", function(e) {
            if (e.key === "Escape") {
                closeAllViewers();
            }
        });
    });
    
})(jQuery);